/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/cantieri/[id]/page";
exports.ids = ["app/cantieri/[id]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcantieri%2F%5Bid%5D%2Fpage&page=%2Fcantieri%2F%5Bid%5D%2Fpage&appPaths=%2Fcantieri%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fcantieri%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CCMS%5Cwebapp-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CCMS%5Cwebapp-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcantieri%2F%5Bid%5D%2Fpage&page=%2Fcantieri%2F%5Bid%5D%2Fpage&appPaths=%2Fcantieri%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fcantieri%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CCMS%5Cwebapp-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CCMS%5Cwebapp-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/cantieri/[id]/page.tsx */ \"(rsc)/./src/app/cantieri/[id]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'cantieri',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/cantieri/[id]/page\",\n        pathname: \"/cantieri/[id]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcantieri%2F%5Bid%5D%2Fpage&page=%2Fcantieri%2F%5Bid%5D%2Fpage&appPaths=%2Fcantieri%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fcantieri%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CCMS%5Cwebapp-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CCMS%5Cwebapp-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Navbar.tsx */ \"(rsc)/./src/components/layout/Navbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNDTVMlNUMlNUN3ZWJhcHAtbmV4dGpzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LWludGVyJTVDJTIyJTJDJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0NNUyU1QyU1Q3dlYmFwcC1uZXh0anMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkpldEJyYWluc19Nb25vJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtamV0YnJhaW5zLW1vbm8lNUMlMjIlMkMlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJqZXRicmFpbnNNb25vJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNDTVMlNUMlNUN3ZWJhcHAtbmV4dGpzJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0NNUyU1QyU1Q3dlYmFwcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0JTVDJTVDTmF2YmFyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMk5hdmJhciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDQ01TJTVDJTVDd2ViYXBwLW5leHRqcyU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBNEg7QUFDNUg7QUFDQSx3S0FBNkgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIk5hdmJhclwiXSAqLyBcIkM6XFxcXENNU1xcXFx3ZWJhcHAtbmV4dGpzXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxOYXZiYXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxDTVNcXFxcd2ViYXBwLW5leHRqc1xcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Ccantieri%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Ccantieri%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/cantieri/[id]/page.tsx */ \"(rsc)/./src/app/cantieri/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNDTVMlNUMlNUN3ZWJhcHAtbmV4dGpzJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDY2FudGllcmklNUMlNUMlNUJpZCU1RCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBK0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXENNU1xcXFx3ZWJhcHAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcY2FudGllcmlcXFxcW2lkXVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Ccantieri%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxDTVNcXHdlYmFwcC1uZXh0anNcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/cantieri/[id]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/cantieri/[id]/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\[id]\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"73833eff14b5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcQ01TXFx3ZWJhcHAtbmV4dGpzXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MzgzM2VmZjE0YjVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(rsc)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"CABLYS - Cable Installation Advance System\",\n    description: \"Sistema avanzato per la gestione dell'installazione cavi\",\n    manifest: \"/manifest.json\",\n    themeColor: \"#2563eb\",\n    viewport: \"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\",\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: \"default\",\n        title: \"CABLYS\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"it\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-slate-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__.Navbar, {}, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"pt-16\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navbar: () => (/* binding */ Navbar)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Navbar = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\CMS\\webapp-nextjs\\src\\components\\layout\\Navbar.tsx",
"Navbar",
);

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\CMS\\webapp-nextjs\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\CMS\\webapp-nextjs\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNDTVMlNUMlNUN3ZWJhcHAtbmV4dGpzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0NNUyU1QyU1Q3dlYmFwcC1uZXh0anMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDQ01TJTVDJTVDd2ViYXBwLW5leHRqcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNDTVMlNUMlNUN3ZWJhcHAtbmV4dGpzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDaHR0cC1hY2Nlc3MtZmFsbGJhY2slNUMlNUNlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDQ01TJTVDJTVDd2ViYXBwLW5leHRqcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0NNUyU1QyU1Q3dlYmFwcC1uZXh0anMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNDTVMlNUMlNUN3ZWJhcHAtbmV4dGpzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbWV0YWRhdGElNUMlNUNtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDQ01TJTVDJTVDd2ViYXBwLW5leHRqcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUF5SDtBQUN6SDtBQUNBLDBPQUE0SDtBQUM1SDtBQUNBLDBPQUE0SDtBQUM1SDtBQUNBLG9SQUFrSjtBQUNsSjtBQUNBLHdPQUEySDtBQUMzSDtBQUNBLDRQQUFzSTtBQUN0STtBQUNBLGtRQUF5STtBQUN6STtBQUNBLHNRQUEwSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcQ01TXFxcXHdlYmFwcC1uZXh0anNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcQ01TXFxcXHdlYmFwcC1uZXh0anNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcQ01TXFxcXHdlYmFwcC1uZXh0anNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcQ01TXFxcXHdlYmFwcC1uZXh0anNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcQ01TXFxcXHdlYmFwcC1uZXh0anNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxDTVNcXFxcd2ViYXBwLW5leHRqc1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXGFzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxDTVNcXFxcd2ViYXBwLW5leHRqc1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxDTVNcXFxcd2ViYXBwLW5leHRqc1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Navbar.tsx */ \"(ssr)/./src/components/layout/Navbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Ccantieri%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Ccantieri%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/cantieri/[id]/page.tsx */ \"(ssr)/./src/app/cantieri/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNDTVMlNUMlNUN3ZWJhcHAtbmV4dGpzJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDY2FudGllcmklNUMlNUMlNUJpZCU1RCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBK0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXENNU1xcXFx3ZWJhcHAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcY2FudGllcmlcXFxcW2lkXVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCMS%5C%5Cwebapp-nextjs%5C%5Csrc%5C%5Capp%5C%5Ccantieri%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/cantieri/[id]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/cantieri/[id]/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CantierePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cable.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart3,Building2,Cable,Calendar,ClipboardList,FileText,Loader2,MapPin,Package,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction CantierePage() {\n    const { user, isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const cantiereId = parseInt(params.id);\n    const [cantiere, setCantiere] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CantierePage.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push('/login');\n            }\n        }\n    }[\"CantierePage.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CantierePage.useEffect\": ()=>{\n            if (isAuthenticated && cantiereId) {\n                loadCantiere();\n            }\n        }\n    }[\"CantierePage.useEffect\"], [\n        isAuthenticated,\n        cantiereId\n    ]);\n    const loadCantiere = async ()=>{\n        try {\n            setLoading(true);\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.cantieriApi.getCantiere(cantiereId);\n            setCantiere(data);\n            // Salva il cantiere selezionato nel localStorage\n            localStorage.setItem('selectedCantiereId', cantiereId.toString());\n            localStorage.setItem('selectedCantiereName', data.commessa);\n        } catch (error) {\n            console.error('Errore nel caricamento cantiere:', error);\n            setError('Errore nel caricamento del cantiere');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBackToCantieri = ()=>{\n        router.push('/cantieri');\n    };\n    const navigateToGestioneCavi = ()=>{\n        router.push('/cavi');\n    };\n    const navigateToParcoCavi = ()=>{\n        router.push('/parco-cavi');\n    };\n    const navigateToComande = ()=>{\n        router.push('/comande');\n    };\n    const navigateToCertificazioni = ()=>{\n        router.push('/certificazioni');\n    };\n    const navigateToReports = ()=>{\n        router.push('/reports');\n    };\n    if (isLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !cantiere) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-4 border border-red-200 rounded-lg bg-red-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 text-red-600 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-800\",\n                                children: error || 'Cantiere non trovato'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    onClick: handleBackToCantieri,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        \"Torna alla Lista Cantieri\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    onClick: handleBackToCantieri,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Torna ai Cantieri\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: cantiere.commessa\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: cantiere.descrizione\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                        variant: \"secondary\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            \"ID: \",\n                            cantiere.id_cantiere\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"Informazioni Cantiere\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: [\n                                cantiere.nome_cliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Cliente\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: cantiere.nome_cliente\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this),\n                                cantiere.indirizzo_cantiere && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Indirizzo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: [\n                                                        cantiere.indirizzo_cantiere,\n                                                        cantiere.citta_cantiere && `, ${cantiere.citta_cantiere}`,\n                                                        cantiere.nazione_cantiere && `, ${cantiere.nazione_cantiere}`\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Data Creazione\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: new Date(cantiere.data_creazione).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"cursor-pointer hover:shadow-lg transition-shadow\",\n                        onClick: navigateToGestioneCavi,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Gestione Cavi\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"Visualizza, aggiungi, modifica e gestisci tutti i cavi del cantiere\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full\",\n                                    children: \"Accedi alla Gestione Cavi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"cursor-pointer hover:shadow-lg transition-shadow\",\n                        onClick: navigateToParcoCavi,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Parco Cavi\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"Gestisci le bobine disponibili e il magazzino cavi\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full\",\n                                    children: \"Accedi al Parco Cavi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"cursor-pointer hover:shadow-lg transition-shadow\",\n                        onClick: navigateToComande,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Gestione Comande\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"Crea e gestisci ordini di lavoro per posa e collegamenti\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full\",\n                                    children: \"Accedi alle Comande\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"cursor-pointer hover:shadow-lg transition-shadow\",\n                        onClick: navigateToCertificazioni,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Certificazioni\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"Gestisci le certificazioni e gli strumenti di misura\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full\",\n                                    children: \"Accedi alle Certificazioni\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"cursor-pointer hover:shadow-lg transition-shadow\",\n                        onClick: navigateToReports,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart3_Building2_Cable_Calendar_ClipboardList_FileText_Loader2_MapPin_Package_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Report e Statistiche\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"Visualizza report di avanzamento e statistiche del cantiere\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full\",\n                                    children: \"Accedi ai Report\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/cantieri/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cable.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Building2,Cable,ClipboardList,FileText,LogOut,Menu,Package,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \n\n\n\n\n\n\n\nconst getNavigation = (userRole)=>{\n    if (userRole === 'owner') {\n        return [\n            {\n                name: 'Amministrazione',\n                href: '/admin',\n                icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            },\n            {\n                name: 'Cantieri',\n                href: '/cantieri',\n                icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n            }\n        ];\n    }\n    if (userRole === 'user') {\n        return [\n            {\n                name: 'Cantieri',\n                href: '/cantieri',\n                icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n            }\n        ];\n    }\n    if (userRole === 'cantieri_user') {\n        return [\n            {\n                name: 'Gestione Cavi',\n                href: '/cavi',\n                icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n            },\n            {\n                name: 'Parco Cavi',\n                href: '/parco-cavi',\n                icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n            },\n            {\n                name: 'Comande',\n                href: '/comande',\n                icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n            },\n            {\n                name: 'Certificazioni',\n                href: '/certificazioni',\n                icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n            },\n            {\n                name: 'Report',\n                href: '/reports',\n                icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n            },\n            {\n                name: 'Produttività',\n                href: '/productivity',\n                icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n            }\n        ];\n    }\n    // Default navigation for authenticated users (cantieri_user)\n    return [\n        {\n            name: 'Gestione Cavi',\n            href: '/cavi',\n            icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            name: 'Parco Cavi',\n            href: '/parco-cavi',\n            icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            name: 'Comande',\n            href: '/comande',\n            icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            name: 'Certificazioni',\n            href: '/certificazioni',\n            icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            name: 'Report',\n            href: '/reports',\n            icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            name: 'Produttività',\n            href: '/productivity',\n            icon: _barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        }\n    ];\n};\nfunction Navbar() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { user, cantiere, isAuthenticated, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const navigation = getNavigation(user?.ruolo);\n    // Non mostrare navbar nella pagina di login\n    if (pathname === '/login') {\n        return null;\n    }\n    // Se non autenticato, non mostrare navbar\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-slate-900\",\n                                                children: \"CABLYS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-slate-500 -mt-1\",\n                                                children: \"Cable Installation System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-1\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href || item.href !== '/' && pathname.startsWith(item.href);\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: isActive ? \"default\" : \"ghost\",\n                                        size: \"sm\",\n                                        className: `flex items-center space-x-2 ${isActive ? 'bg-blue-600 text-white hover:bg-blue-700' : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden lg:inline\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-slate-900\",\n                                                    children: user ? user.username : cantiere?.commessa\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-slate-500\",\n                                                    children: user ? user.ruolo : 'Cantiere'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\",\n                                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 25\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 68\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"bg-green-100 text-green-800\",\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: logout,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setIsOpen(!isOpen),\n                                        className: \"text-slate-600\",\n                                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 27\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 55\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden border-t border-slate-200 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1\",\n                        children: navigation.map((item)=>{\n                            const isActive = pathname === item.href || item.href !== '/' && pathname.startsWith(item.href);\n                            const Icon = item.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: isActive ? \"default\" : \"ghost\",\n                                    size: \"sm\",\n                                    className: `w-full justify-start space-x-3 ${isActive ? 'bg-blue-600 text-white' : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'}`,\n                                    onClick: ()=>setIsOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.name, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-slate-200 px-4 py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Building2_Cable_ClipboardList_FileText_LogOut_Menu_Package_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-slate-900\",\n                                            children: \"Admin User\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-500\",\n                                            children: \"Cantiere Demo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"bg-green-100 text-green-800 ml-auto\",\n                                    children: \"Online\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n            destructive: \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"span\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"badge\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwrYkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FDRTtZQUNGQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUNFO1lBQ0ZDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBR0YsU0FBU0ssT0FBTyxFQUNkQyxTQUFTLEVBQ1RiLE9BQU8sRUFDUE8sSUFBSSxFQUNKTyxVQUFVLEtBQUssRUFDZixHQUFHQyxPQUlGO0lBQ0QsTUFBTUMsT0FBT0YsVUFBVW5CLHNEQUFJQSxHQUFHO0lBRTlCLHFCQUNFLDhEQUFDcUI7UUFDQ0MsYUFBVTtRQUNWSixXQUFXaEIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU07UUFBVTtRQUN2RCxHQUFHRSxLQUFLOzs7Ozs7QUFHZjtBQUVpQyIsInNvdXJjZXMiOlsiQzpcXENNU1xcd2ViYXBwLW5leHRqc1xcc3JjXFxjb21wb25lbnRzXFx1aVxcYnV0dG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgU2xvdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3Qtc2xvdFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXG4gIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIHdoaXRlc3BhY2Utbm93cmFwIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgWyZfc3ZnXTpwb2ludGVyLWV2ZW50cy1ub25lIFsmX3N2Zzpub3QoW2NsYXNzKj0nc2l6ZS0nXSldOnNpemUtNCBzaHJpbmstMCBbJl9zdmddOnNocmluay0wIG91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOmJvcmRlci1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1yaW5nLzUwIGZvY3VzLXZpc2libGU6cmluZy1bM3B4XSBhcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS8yMCBkYXJrOmFyaWEtaW52YWxpZDpyaW5nLWRlc3RydWN0aXZlLzQwIGFyaWEtaW52YWxpZDpib3JkZXItZGVzdHJ1Y3RpdmVcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgXCJiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIHNoYWRvdy14cyBob3ZlcjpiZy1wcmltYXJ5LzkwXCIsXG4gICAgICAgIGRlc3RydWN0aXZlOlxuICAgICAgICAgIFwiYmctZGVzdHJ1Y3RpdmUgdGV4dC13aGl0ZSBzaGFkb3cteHMgaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTAgZm9jdXMtdmlzaWJsZTpyaW5nLWRlc3RydWN0aXZlLzIwIGRhcms6Zm9jdXMtdmlzaWJsZTpyaW5nLWRlc3RydWN0aXZlLzQwIGRhcms6YmctZGVzdHJ1Y3RpdmUvNjBcIixcbiAgICAgICAgb3V0bGluZTpcbiAgICAgICAgICBcImJvcmRlciBiZy1iYWNrZ3JvdW5kIHNoYWRvdy14cyBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBkYXJrOmJnLWlucHV0LzMwIGRhcms6Ym9yZGVyLWlucHV0IGRhcms6aG92ZXI6YmctaW5wdXQvNTBcIixcbiAgICAgICAgc2Vjb25kYXJ5OlxuICAgICAgICAgIFwiYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgc2hhZG93LXhzIGhvdmVyOmJnLXNlY29uZGFyeS84MFwiLFxuICAgICAgICBnaG9zdDpcbiAgICAgICAgICBcImhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGRhcms6aG92ZXI6YmctYWNjZW50LzUwXCIsXG4gICAgICAgIGxpbms6IFwidGV4dC1wcmltYXJ5IHVuZGVybGluZS1vZmZzZXQtNCBob3Zlcjp1bmRlcmxpbmVcIixcbiAgICAgIH0sXG4gICAgICBzaXplOiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiaC05IHB4LTQgcHktMiBoYXMtWz5zdmddOnB4LTNcIixcbiAgICAgICAgc206IFwiaC04IHJvdW5kZWQtbWQgZ2FwLTEuNSBweC0zIGhhcy1bPnN2Z106cHgtMi41XCIsXG4gICAgICAgIGxnOiBcImgtMTAgcm91bmRlZC1tZCBweC02IGhhcy1bPnN2Z106cHgtNFwiLFxuICAgICAgICBpY29uOiBcInNpemUtOVwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmZ1bmN0aW9uIEJ1dHRvbih7XG4gIGNsYXNzTmFtZSxcbiAgdmFyaWFudCxcbiAgc2l6ZSxcbiAgYXNDaGlsZCA9IGZhbHNlLFxuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJidXR0b25cIj4gJlxuICBWYXJpYW50UHJvcHM8dHlwZW9mIGJ1dHRvblZhcmlhbnRzPiAmIHtcbiAgICBhc0NoaWxkPzogYm9vbGVhblxuICB9KSB7XG4gIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IFwiYnV0dG9uXCJcblxuICByZXR1cm4gKFxuICAgIDxDb21wXG4gICAgICBkYXRhLXNsb3Q9XCJidXR0b25cIlxuICAgICAgY2xhc3NOYW1lPXtjbihidXR0b25WYXJpYW50cyh7IHZhcmlhbnQsIHNpemUsIGNsYXNzTmFtZSB9KSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNsb3QiLCJjdmEiLCJjbiIsImJ1dHRvblZhcmlhbnRzIiwidmFyaWFudHMiLCJ2YXJpYW50IiwiZGVmYXVsdCIsImRlc3RydWN0aXZlIiwib3V0bGluZSIsInNlY29uZGFyeSIsImdob3N0IiwibGluayIsInNpemUiLCJzbSIsImxnIiwiaWNvbiIsImRlZmF1bHRWYXJpYW50cyIsIkJ1dHRvbiIsImNsYXNzTmFtZSIsImFzQ2hpbGQiLCJwcm9wcyIsIkNvbXAiLCJkYXRhLXNsb3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cantiere, setCantiere] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isImpersonating, setIsImpersonating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"AuthProvider.useState\": ()=>{\n            if (false) {}\n            return false;\n        }\n    }[\"AuthProvider.useState\"]);\n    const [impersonatedUser, setImpersonatedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"AuthProvider.useState\": ()=>{\n            if (false) {}\n            return null;\n        }\n    }[\"AuthProvider.useState\"]);\n    const isAuthenticated = !!user || !!cantiere;\n    // Verifica l'autenticazione al caricamento\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            console.log('Verificando autenticazione all\\'avvio...');\n            // Verifica se siamo nel browser\n            if (true) {\n                setIsLoading(false);\n                return;\n            }\n            // Prima di tutto, imposta loading a true\n            setIsLoading(true);\n            // Pulisci eventuali token non validi o scaduti\n            const token = localStorage.getItem('token');\n            console.log('Token trovato nel localStorage:', token ? 'Sì' : 'No');\n            if (token) {\n                try {\n                    // Verifica la validità del token\n                    console.log('Tentativo di verifica token...');\n                    const userData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.verifyToken();\n                    console.log('Token valido, dati utente:', userData);\n                    // Imposta i dati dell'utente come nel sistema React originale\n                    const userInfo = {\n                        id_utente: userData.user_id,\n                        username: userData.username,\n                        ruolo: userData.role\n                    };\n                    setUser(userInfo);\n                    // Gestisci l'impersonificazione\n                    const impersonatingState = userData.is_impersonated === true;\n                    console.log('Stato di impersonificazione recuperato dai dati utente:', impersonatingState);\n                    setIsImpersonating(impersonatingState);\n                    if (impersonatingState && userData.impersonated_id) {\n                        const impersonatedUserData = {\n                            id: userData.impersonated_id,\n                            username: userData.impersonated_username,\n                            role: userData.impersonated_role\n                        };\n                        setImpersonatedUser(impersonatedUserData);\n                        if (false) {}\n                    } else {\n                        setImpersonatedUser(null);\n                        if (false) {}\n                    }\n                    // Se è un utente cantiere, gestisci i dati del cantiere\n                    if (userData.role === 'cantieri_user' && userData.cantiere_id) {\n                        const cantiereData = {\n                            id_cantiere: userData.cantiere_id,\n                            commessa: userData.cantiere_name || `Cantiere ${userData.cantiere_id}`,\n                            codice_univoco: '',\n                            id_utente: userData.user_id\n                        };\n                        setCantiere(cantiereData);\n                    }\n                } catch (tokenError) {\n                    console.error('Errore durante la verifica del token:', tokenError);\n                    // Se il token non è valido, rimuovilo\n                    console.log('Rimozione token non valido dal localStorage');\n                    localStorage.removeItem('token');\n                    localStorage.removeItem('access_token');\n                    localStorage.removeItem('user_data');\n                    localStorage.removeItem('cantiere_data');\n                    setUser(null);\n                    setCantiere(null);\n                }\n            } else {\n                console.log('Nessun token trovato, utente non autenticato');\n                setUser(null);\n                setCantiere(null);\n            }\n        } catch (error) {\n            console.error('Errore generale durante la verifica dell\\'autenticazione:', error);\n            // In caso di errore generale, assicurati che l'utente non sia autenticato\n            if (false) {}\n            setUser(null);\n            setCantiere(null);\n        } finally{\n            // Assicurati che loading sia impostato a false alla fine\n            console.log('Completata verifica autenticazione, loading:', false);\n            setTimeout(()=>{\n                setIsLoading(false);\n            }, 500) // Aggiungi un piccolo ritardo come nel sistema React originale\n            ;\n        }\n    };\n    const login = async (username, password)=>{\n        try {\n            console.log('Tentativo di login utente:', username);\n            setIsLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                username,\n                password\n            });\n            console.log('Risposta login ricevuta:', response);\n            if (false) {}\n        } catch (error) {\n            console.error('Errore login:', error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loginCantiere = async (codice_cantiere, password_cantiere)=>{\n        try {\n            console.log('Tentativo di login cantiere:', codice_cantiere);\n            setIsLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.loginCantiere({\n                codice_cantiere,\n                password_cantiere\n            });\n            console.log('Risposta login cantiere ricevuta:', response);\n            if (false) {}\n        } catch (error) {\n            console.error('Errore login cantiere:', error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const impersonateUser = async (userId)=>{\n        try {\n            // Chiama l'endpoint di impersonificazione\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.usersApi.impersonateUser(userId);\n            if (false) {}\n        } catch (error) {\n            console.error('Errore durante l\\'impersonificazione:', error);\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        console.log('Logout, controllo se è impersonificazione...');\n        if (false) {}\n    };\n    const value = {\n        user,\n        cantiere,\n        isAuthenticated,\n        isLoading,\n        isImpersonating,\n        impersonatedUser,\n        login,\n        loginCantiere,\n        logout,\n        checkAuth,\n        impersonateUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 309,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   cantieriApi: () => (/* binding */ cantieriApi),\n/* harmony export */   caviApi: () => (/* binding */ caviApi),\n/* harmony export */   comandeApi: () => (/* binding */ comandeApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   parcoCaviApi: () => (/* binding */ parcoCaviApi),\n/* harmony export */   reportsApi: () => (/* binding */ reportsApi),\n/* harmony export */   responsabiliApi: () => (/* binding */ responsabiliApi),\n/* harmony export */   usersApi: () => (/* binding */ usersApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// Configurazione base per l'API\nconst API_BASE_URL = \"http://localhost:8001\" || 0;\n// Crea istanza axios con configurazione base\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 30000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Interceptor per aggiungere il token di autenticazione\napiClient.interceptors.request.use((config)=>{\n    // Verifica se siamo nel browser prima di accedere a localStorage\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Interceptor per gestire le risposte e gli errori\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401 && \"undefined\" !== 'undefined') {}\n    return Promise.reject(error);\n});\n// Funzioni helper per le chiamate API\nconst api = {\n    // GET request\n    get: async (url, config)=>{\n        const response = await apiClient.get(url, config);\n        return response.data;\n    },\n    // POST request\n    post: async (url, data, config)=>{\n        const response = await apiClient.post(url, data, config);\n        return response.data;\n    },\n    // PUT request\n    put: async (url, data, config)=>{\n        const response = await apiClient.put(url, data, config);\n        return response.data;\n    },\n    // PATCH request\n    patch: async (url, data, config)=>{\n        const response = await apiClient.patch(url, data, config);\n        return response.data;\n    },\n    // DELETE request\n    delete: async (url, config)=>{\n        const response = await apiClient.delete(url, config);\n        return response.data;\n    }\n};\n// Servizi API specifici per CABLYS\nconst authApi = {\n    // Login utente - usa FormData per OAuth2PasswordRequestForm\n    login: async (credentials)=>{\n        const formData = new FormData();\n        formData.append('username', credentials.username);\n        formData.append('password', credentials.password);\n        const response = await apiClient.post('/api/auth/login', formData, {\n            headers: {\n                'Content-Type': 'application/x-www-form-urlencoded'\n            }\n        });\n        return response.data;\n    },\n    // Login cantiere - usa JSON per CantiereLogin\n    loginCantiere: (credentials)=>api.post('/api/auth/login/cantiere', {\n            codice_univoco: credentials.codice_cantiere,\n            password: credentials.password_cantiere\n        }),\n    // Verifica token\n    verifyToken: ()=>api.post('/api/auth/test-token'),\n    // Logout\n    logout: ()=>{\n        localStorage.removeItem('access_token');\n        localStorage.removeItem('user_data');\n        window.location.href = '/login';\n    }\n};\nconst caviApi = {\n    // Ottieni tutti i cavi\n    getCavi: (cantiereId, params)=>api.get(`/api/cavi/${cantiereId}`, {\n            params\n        }),\n    // Ottieni cavo specifico\n    getCavo: (cantiereId, idCavo)=>api.get(`/api/cavi/${cantiereId}/${idCavo}`),\n    // Crea nuovo cavo\n    createCavo: (cantiereId, cavo)=>api.post(`/api/cavi/${cantiereId}`, cavo),\n    // Aggiorna cavo\n    updateCavo: (cantiereId, idCavo, updates)=>api.put(`/api/cavi/${cantiereId}/${idCavo}`, updates),\n    // Elimina cavo\n    deleteCavo: (cantiereId, idCavo)=>api.delete(`/api/cavi/${cantiereId}/${idCavo}`),\n    // Aggiorna metri posati\n    updateMetriPosati: (cantiereId, idCavo, metri)=>api.patch(`/api/cavi/${cantiereId}/${idCavo}/metri-posati`, {\n            metri_posati: metri\n        }),\n    // Aggiorna bobina\n    updateBobina: (cantiereId, idCavo, bobina)=>api.patch(`/api/cavi/${cantiereId}/${idCavo}/bobina`, {\n            id_bobina: bobina\n        }),\n    // Aggiorna collegamento\n    updateCollegamento: (cantiereId, idCavo, collegamento)=>api.patch(`/api/cavi/${cantiereId}/${idCavo}/collegamento`, {\n            collegamenti: collegamento\n        })\n};\nconst parcoCaviApi = {\n    // Ottieni tutte le bobine\n    getBobine: (cantiereId)=>api.get(`/api/parco-cavi/${cantiereId}`),\n    // Ottieni bobina specifica\n    getBobina: (cantiereId, idBobina)=>api.get(`/api/parco-cavi/${cantiereId}/${idBobina}`),\n    // Crea nuova bobina\n    createBobina: (cantiereId, bobina)=>api.post(`/api/parco-cavi/${cantiereId}`, bobina),\n    // Aggiorna bobina\n    updateBobina: (cantiereId, idBobina, updates)=>api.put(`/api/parco-cavi/${cantiereId}/${idBobina}`, updates),\n    // Elimina bobina\n    deleteBobina: (cantiereId, idBobina)=>api.delete(`/api/parco-cavi/${cantiereId}/${idBobina}`)\n};\nconst comandeApi = {\n    // Ottieni tutte le comande\n    getComande: (cantiereId)=>api.get(`/api/comande/${cantiereId}`),\n    // Ottieni comanda specifica\n    getComanda: (cantiereId, codiceComanda)=>api.get(`/api/comande/${cantiereId}/${codiceComanda}`),\n    // Crea nuova comanda\n    createComanda: (cantiereId, comanda)=>api.post(`/api/comande/${cantiereId}`, comanda),\n    // Aggiorna comanda\n    updateComanda: (cantiereId, codiceComanda, updates)=>api.put(`/api/comande/${cantiereId}/${codiceComanda}`, updates),\n    // Elimina comanda\n    deleteComanda: (cantiereId, codiceComanda)=>api.delete(`/api/comande/${cantiereId}/${codiceComanda}`),\n    // Assegna cavi a comanda\n    assegnaCavi: (cantiereId, codiceComanda, caviIds)=>api.post(`/api/comande/${cantiereId}/${codiceComanda}/assegna-cavi`, {\n            cavi_ids: caviIds\n        })\n};\nconst responsabiliApi = {\n    // Ottieni tutti i responsabili\n    getResponsabili: (cantiereId)=>api.get(`/api/responsabili/${cantiereId}`),\n    // Crea nuovo responsabile\n    createResponsabile: (cantiereId, responsabile)=>api.post(`/api/responsabili/${cantiereId}`, responsabile),\n    // Aggiorna responsabile\n    updateResponsabile: (cantiereId, id, updates)=>api.put(`/api/responsabili/${cantiereId}/${id}`, updates),\n    // Elimina responsabile\n    deleteResponsabile: (cantiereId, id)=>api.delete(`/api/responsabili/${cantiereId}/${id}`)\n};\nconst reportsApi = {\n    // Report avanzamento\n    getReportAvanzamento: (cantiereId)=>api.get(`/api/reports/${cantiereId}/avanzamento`),\n    // Report BOQ\n    getReportBOQ: (cantiereId)=>api.get(`/api/reports/${cantiereId}/boq`),\n    // Report utilizzo bobine\n    getReportUtilizzoBobine: (cantiereId)=>api.get(`/api/reports/${cantiereId}/utilizzo-bobine`),\n    // Report progress\n    getReportProgress: (cantiereId)=>api.get(`/api/reports/${cantiereId}/progress`)\n};\nconst cantieriApi = {\n    // Ottieni tutti i cantieri\n    getCantieri: ()=>api.get('/api/cantieri'),\n    // Ottieni cantiere specifico\n    getCantiere: (id)=>api.get(`/api/cantieri/${id}`),\n    // Crea nuovo cantiere\n    createCantiere: (cantiere)=>api.post('/api/cantieri', cantiere),\n    // Aggiorna cantiere\n    updateCantiere: (id, updates)=>api.put(`/api/cantieri/${id}`, updates)\n};\nconst usersApi = {\n    // Ottieni tutti gli utenti (solo admin)\n    getUsers: ()=>api.get('/api/users'),\n    // Ottieni utente specifico\n    getUser: (id)=>api.get(`/api/users/${id}`),\n    // Crea nuovo utente\n    createUser: (user)=>api.post('/api/users', user),\n    // Aggiorna utente\n    updateUser: (id, updates)=>api.put(`/api/users/${id}`, updates),\n    // Elimina utente\n    deleteUser: (id)=>api.delete(`/api/users/${id}`),\n    // Abilita/Disabilita utente\n    toggleUserStatus: (id)=>api.get(`/api/users/toggle/${id}`),\n    // Verifica utenti scaduti\n    checkExpiredUsers: ()=>api.get('/api/users/check-expired'),\n    // Impersona utente\n    impersonateUser: (userId)=>api.post('/api/auth/impersonate', {\n            user_id: userId\n        }),\n    // Ottieni dati database raw\n    getDatabaseData: ()=>api.get('/api/users/db-raw'),\n    // Reset database\n    resetDatabase: ()=>api.post('/api/admin/reset-database')\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiQzpcXENNU1xcd2ViYXBwLW5leHRqc1xcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/class-variance-authority","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcantieri%2F%5Bid%5D%2Fpage&page=%2Fcantieri%2F%5Bid%5D%2Fpage&appPaths=%2Fcantieri%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fcantieri%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CCMS%5Cwebapp-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CCMS%5Cwebapp-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();