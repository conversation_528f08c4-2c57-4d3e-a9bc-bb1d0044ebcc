{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/compact-actions-dropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { ChevronDown, Edit, Clock, CheckCircle, Trash2 } from 'lucide-react'\n\ninterface CompactActionsDropdownProps {\n  user: {\n    id_utente: number\n    ruolo: string\n    abilitato: boolean\n  }\n  onEdit: () => void\n  onToggleStatus: () => void\n  onDelete: () => void\n}\n\nexport default function CompactActionsDropdown({ \n  user, \n  onEdit, \n  onToggleStatus, \n  onDelete \n}: CompactActionsDropdownProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n\n  // Chiudi dropdown quando si clicca fuori\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  const handleAction = (action: () => void) => {\n    action()\n    setIsOpen(false)\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Triangolino piccolo e discreto */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"p-1 rounded hover:bg-slate-100 transition-colors cursor-pointer\"\n        title=\"Azioni\"\n      >\n        <ChevronDown className={`h-3 w-3 text-slate-600 transition-transform duration-200 ${\n          isOpen ? 'rotate-180' : ''\n        }`} />\n      </button>\n\n      {/* Dropdown Menu */}\n      {isOpen && (\n        <div className=\"absolute right-0 top-full mt-1 w-36 bg-white border border-slate-200 rounded-md shadow-lg z-[100]\">\n          <div className=\"py-1\">\n            {/* Modifica - sempre disponibile */}\n            <button\n              onClick={() => handleAction(onEdit)}\n              className=\"w-full px-3 py-2 text-left text-sm flex items-center gap-2 hover:bg-slate-50 transition-colors\"\n            >\n              <Edit className=\"h-3.5 w-3.5 text-slate-600\" />\n              <span>Modifica</span>\n            </button>\n\n            {/* Abilita/Disabilita */}\n            <button\n              onClick={() => handleAction(onToggleStatus)}\n              disabled={user.ruolo === 'owner'}\n              className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${\n                user.ruolo === 'owner'\n                  ? 'opacity-50 cursor-not-allowed bg-slate-50'\n                  : 'hover:bg-slate-50'\n              }`}\n            >\n              {user.abilitato ? (\n                <>\n                  <Clock className=\"h-3.5 w-3.5 text-red-500\" />\n                  <span>Disabilita</span>\n                </>\n              ) : (\n                <>\n                  <CheckCircle className=\"h-3.5 w-3.5 text-green-500\" />\n                  <span>Abilita</span>\n                </>\n              )}\n            </button>\n\n            {/* Elimina */}\n            <button\n              onClick={() => handleAction(onDelete)}\n              disabled={user.ruolo === 'owner'}\n              className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${\n                user.ruolo === 'owner'\n                  ? 'opacity-50 cursor-not-allowed bg-slate-50'\n                  : 'hover:bg-red-50 text-red-600'\n              }`}\n            >\n              <Trash2 className=\"h-3.5 w-3.5 text-red-500\" />\n              <span>Elimina</span>\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAgBe,SAAS,uBAAuB,EAC7C,IAAI,EACJ,MAAM,EACN,cAAc,EACd,QAAQ,EACoB;IAC5B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB;QACA,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,OAAM;0BAEN,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAW,CAAC,yDAAyD,EAChF,SAAS,eAAe,IACxB;;;;;;;;;;;YAIH,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;;8CAEV,8OAAC,2MAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;;;;;;sCAIR,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,UAAU,KAAK,KAAK,KAAK;4BACzB,WAAW,CAAC,6EAA6E,EACvF,KAAK,KAAK,KAAK,UACX,8CACA,qBACJ;sCAED,KAAK,SAAS,iBACb;;kDACE,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;6DAGR;;kDACE,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;kDAAK;;;;;;;;;;;;;sCAMZ,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,UAAU,KAAK,KAAK,KAAK;4BACzB,WAAW,CAAC,6EAA6E,EACvF,KAAK,KAAK,KAAK,UACX,8CACA,gCACJ;;8CAEF,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/utils/securityValidation.ts"], "sourcesContent": ["/**\n * 🔒 SECURITY VALIDATION UTILITIES\n * Validazione robusta per prevenire attacchi di sicurezza\n */\n\n// Caratteri pericolosi da rimuovere/escape\nconst DANGEROUS_CHARS = /[<>\\\"'&\\x00-\\x1f\\x7f-\\x9f]/g\nconst SQL_INJECTION_PATTERNS = /(\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\\b)/gi\nconst XSS_PATTERNS = /(<script|javascript:|vbscript:|onload|onerror|onclick)/gi\n\n/**\n * Sanitizza input rimuovendo caratteri pericolosi\n */\nexport const sanitizeInput = (input: string): string => {\n  if (typeof input !== 'string') return ''\n  \n  return input\n    .trim()\n    .replace(DANGEROUS_CHARS, '') // Rimuove caratteri pericolosi\n    .replace(/\\s+/g, ' ') // Normalizza spazi\n    .substring(0, 1000) // <PERSON><PERSON> lunghezza massima\n}\n\n/**\n * Valida username con regole sicure\n */\nexport const validateUsername = (username: string): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(username)\n  \n  if (sanitized.length < 3) {\n    return { isValid: false, error: 'Username deve essere almeno 3 caratteri' }\n  }\n  \n  if (sanitized.length > 20) {\n    return { isValid: false, error: 'Username non può superare 20 caratteri' }\n  }\n  \n  if (!/^[a-zA-Z0-9._-]+$/.test(sanitized)) {\n    return { isValid: false, error: 'Username può contenere solo lettere, numeri, punti, underscore e trattini' }\n  }\n  \n  if (/^[._-]|[._-]$/.test(sanitized)) {\n    return { isValid: false, error: 'Username non può iniziare o finire con caratteri speciali' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida password con regole di sicurezza\n */\nexport const validatePassword = (password: string): { isValid: boolean; error?: string; strength: number } => {\n  if (!password || password.length < 8) {\n    return { isValid: false, error: 'Password deve essere almeno 8 caratteri', strength: 0 }\n  }\n  \n  if (password.length > 128) {\n    return { isValid: false, error: 'Password troppo lunga (max 128 caratteri)', strength: 0 }\n  }\n  \n  let strength = 0\n  \n  // Controlla criteri di sicurezza\n  if (/[a-z]/.test(password)) strength++\n  if (/[A-Z]/.test(password)) strength++\n  if (/[0-9]/.test(password)) strength++\n  if (/[^a-zA-Z0-9]/.test(password)) strength++\n  if (password.length >= 12) strength++\n  \n  if (strength < 3) {\n    return { \n      isValid: false, \n      error: 'Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale',\n      strength \n    }\n  }\n  \n  // Controlla password comuni\n  const commonPasswords = ['password', '123456', 'admin', 'qwerty', 'letmein']\n  if (commonPasswords.some(common => password.toLowerCase().includes(common))) {\n    return { isValid: false, error: 'Password troppo comune', strength }\n  }\n  \n  return { isValid: true, strength }\n}\n\n/**\n * Valida email con controlli di sicurezza\n */\nexport const validateEmail = (email: string): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(email)\n  \n  if (!sanitized) {\n    return { isValid: false, error: 'Email è obbligatoria' }\n  }\n  \n  if (sanitized.length > 254) {\n    return { isValid: false, error: 'Email troppo lunga' }\n  }\n  \n  // Regex RFC 5322 semplificata ma sicura\n  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/\n  \n  if (!emailRegex.test(sanitized)) {\n    return { isValid: false, error: 'Formato email non valido' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida testo generico con controlli XSS\n */\nexport const validateText = (text: string, maxLength: number = 255): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(text)\n  \n  if (sanitized.length > maxLength) {\n    return { isValid: false, error: `Testo troppo lungo (max ${maxLength} caratteri)` }\n  }\n  \n  // Controlla pattern XSS\n  if (XSS_PATTERNS.test(text)) {\n    return { isValid: false, error: 'Contenuto non consentito rilevato' }\n  }\n  \n  // Controlla pattern SQL injection\n  if (SQL_INJECTION_PATTERNS.test(text)) {\n    return { isValid: false, error: 'Contenuto non consentito rilevato' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida ragione sociale con controlli business\n */\nexport const validateRagioneSociale = (ragioneSociale: string): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(ragioneSociale)\n  \n  if (!sanitized) {\n    return { isValid: false, error: 'Ragione sociale è obbligatoria' }\n  }\n  \n  if (sanitized.length < 2) {\n    return { isValid: false, error: 'Ragione sociale troppo corta' }\n  }\n  \n  if (sanitized.length > 100) {\n    return { isValid: false, error: 'Ragione sociale troppo lunga (max 100 caratteri)' }\n  }\n  \n  // Solo lettere, numeri, spazi e caratteri business comuni\n  if (!/^[a-zA-Z0-9\\s\\.\\-&']+$/.test(sanitized)) {\n    return { isValid: false, error: 'Ragione sociale contiene caratteri non consentiti' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida codice VAT/Partita IVA\n */\nexport const validateVAT = (vat: string): { isValid: boolean; error?: string } => {\n  if (!vat) return { isValid: true } // VAT è opzionale\n  \n  const sanitized = sanitizeInput(vat).replace(/\\s/g, '') // Rimuove spazi\n  \n  if (sanitized.length < 8 || sanitized.length > 15) {\n    return { isValid: false, error: 'VAT deve essere tra 8 e 15 caratteri' }\n  }\n  \n  // Solo numeri e lettere per VAT internazionali\n  if (!/^[A-Z0-9]+$/i.test(sanitized)) {\n    return { isValid: false, error: 'VAT può contenere solo lettere e numeri' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Rate limiting semplice (in-memory)\n */\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>()\n\nexport const checkRateLimit = (key: string, maxAttempts: number, windowMs: number): boolean => {\n  const now = Date.now()\n  const record = rateLimitStore.get(key)\n  \n  if (!record || now > record.resetTime) {\n    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })\n    return true\n  }\n  \n  if (record.count >= maxAttempts) {\n    return false\n  }\n  \n  record.count++\n  return true\n}\n\n/**\n * Genera token CSRF sicuro\n */\nexport const generateCSRFToken = (): string => {\n  const array = new Uint8Array(32)\n  crypto.getRandomValues(array)\n  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')\n}\n\n/**\n * Valida form completo utente\n */\nexport const validateUserForm = (formData: {\n  username: string\n  password?: string\n  ragione_sociale: string\n  email?: string\n  vat?: string\n  indirizzo?: string\n  nazione?: string\n  referente_aziendale?: string\n}): { isValid: boolean; errors: Record<string, string> } => {\n  const errors: Record<string, string> = {}\n  \n  // Valida username\n  const usernameValidation = validateUsername(formData.username)\n  if (!usernameValidation.isValid) {\n    errors.username = usernameValidation.error!\n  }\n  \n  // Valida password (se presente)\n  if (formData.password) {\n    const passwordValidation = validatePassword(formData.password)\n    if (!passwordValidation.isValid) {\n      errors.password = passwordValidation.error!\n    }\n  }\n  \n  // Valida ragione sociale\n  const ragioneSocialeValidation = validateRagioneSociale(formData.ragione_sociale)\n  if (!ragioneSocialeValidation.isValid) {\n    errors.ragione_sociale = ragioneSocialeValidation.error!\n  }\n  \n  // Valida email (se presente)\n  if (formData.email) {\n    const emailValidation = validateEmail(formData.email)\n    if (!emailValidation.isValid) {\n      errors.email = emailValidation.error!\n    }\n  }\n  \n  // Valida VAT (se presente)\n  if (formData.vat) {\n    const vatValidation = validateVAT(formData.vat)\n    if (!vatValidation.isValid) {\n      errors.vat = vatValidation.error!\n    }\n  }\n  \n  // Valida campi testo opzionali\n  if (formData.indirizzo) {\n    const indirizzoValidation = validateText(formData.indirizzo, 200)\n    if (!indirizzoValidation.isValid) {\n      errors.indirizzo = indirizzoValidation.error!\n    }\n  }\n  \n  if (formData.nazione) {\n    const nazioneValidation = validateText(formData.nazione, 50)\n    if (!nazioneValidation.isValid) {\n      errors.nazione = nazioneValidation.error!\n    }\n  }\n  \n  if (formData.referente_aziendale) {\n    const referenteValidation = validateText(formData.referente_aziendale, 100)\n    if (!referenteValidation.isValid) {\n      errors.referente_aziendale = referenteValidation.error!\n    }\n  }\n  \n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,2CAA2C;;;;;;;;;;;;;AAC3C,MAAM,kBAAkB;AACxB,MAAM,yBAAyB;AAC/B,MAAM,eAAe;AAKd,MAAM,gBAAgB,CAAC;IAC5B,IAAI,OAAO,UAAU,UAAU,OAAO;IAEtC,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,iBAAiB,IAAI,+BAA+B;KAC5D,OAAO,CAAC,QAAQ,KAAK,mBAAmB;KACxC,SAAS,CAAC,GAAG,MAAM,2BAA2B;;AACnD;AAKO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,YAAY,cAAc;IAEhC,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;IAEA,IAAI,UAAU,MAAM,GAAG,IAAI;QACzB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAyC;IAC3E;IAEA,IAAI,CAAC,oBAAoB,IAAI,CAAC,YAAY;QACxC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4E;IAC9G;IAEA,IAAI,gBAAgB,IAAI,CAAC,YAAY;QACnC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4D;IAC9F;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,CAAC,YAAY,SAAS,MAAM,GAAG,GAAG;QACpC,OAAO;YAAE,SAAS;YAAO,OAAO;YAA2C,UAAU;QAAE;IACzF;IAEA,IAAI,SAAS,MAAM,GAAG,KAAK;QACzB,OAAO;YAAE,SAAS;YAAO,OAAO;YAA6C,UAAU;QAAE;IAC3F;IAEA,IAAI,WAAW;IAEf,iCAAiC;IACjC,IAAI,QAAQ,IAAI,CAAC,WAAW;IAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;IAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;IAC5B,IAAI,eAAe,IAAI,CAAC,WAAW;IACnC,IAAI,SAAS,MAAM,IAAI,IAAI;IAE3B,IAAI,WAAW,GAAG;QAChB,OAAO;YACL,SAAS;YACT,OAAO;YACP;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB;QAAC;QAAY;QAAU;QAAS;QAAU;KAAU;IAC5E,IAAI,gBAAgB,IAAI,CAAC,CAAA,SAAU,SAAS,WAAW,GAAG,QAAQ,CAAC,UAAU;QAC3E,OAAO;YAAE,SAAS;YAAO,OAAO;YAA0B;QAAS;IACrE;IAEA,OAAO;QAAE,SAAS;QAAM;IAAS;AACnC;AAKO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,YAAY,cAAc;IAEhC,IAAI,CAAC,WAAW;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuB;IACzD;IAEA,IAAI,UAAU,MAAM,GAAG,KAAK;QAC1B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqB;IACvD;IAEA,wCAAwC;IACxC,MAAM,aAAa;IAEnB,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY;QAC/B,OAAO;YAAE,SAAS;YAAO,OAAO;QAA2B;IAC7D;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,eAAe,CAAC,MAAc,YAAoB,GAAG;IAChE,MAAM,YAAY,cAAc;IAEhC,IAAI,UAAU,MAAM,GAAG,WAAW;QAChC,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,wBAAwB,EAAE,UAAU,WAAW,CAAC;QAAC;IACpF;IAEA,wBAAwB;IACxB,IAAI,aAAa,IAAI,CAAC,OAAO;QAC3B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,kCAAkC;IAClC,IAAI,uBAAuB,IAAI,CAAC,OAAO;QACrC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,yBAAyB,CAAC;IACrC,MAAM,YAAY,cAAc;IAEhC,IAAI,CAAC,WAAW;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAAiC;IACnE;IAEA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;IAEA,IAAI,UAAU,MAAM,GAAG,KAAK;QAC1B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmD;IACrF;IAEA,0DAA0D;IAC1D,IAAI,CAAC,yBAAyB,IAAI,CAAC,YAAY;QAC7C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoD;IACtF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,KAAK,OAAO;QAAE,SAAS;IAAK,EAAE,kBAAkB;;IAErD,MAAM,YAAY,cAAc,KAAK,OAAO,CAAC,OAAO,IAAI,gBAAgB;;IAExE,IAAI,UAAU,MAAM,GAAG,KAAK,UAAU,MAAM,GAAG,IAAI;QACjD,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuC;IACzE;IAEA,+CAA+C;IAC/C,IAAI,CAAC,eAAe,IAAI,CAAC,YAAY;QACnC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA;;CAEC,GACD,MAAM,iBAAiB,IAAI;AAEpB,MAAM,iBAAiB,CAAC,KAAa,aAAqB;IAC/D,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,SAAS,eAAe,GAAG,CAAC;IAElC,IAAI,CAAC,UAAU,MAAM,OAAO,SAAS,EAAE;QACrC,eAAe,GAAG,CAAC,KAAK;YAAE,OAAO;YAAG,WAAW,MAAM;QAAS;QAC9D,OAAO;IACT;IAEA,IAAI,OAAO,KAAK,IAAI,aAAa;QAC/B,OAAO;IACT;IAEA,OAAO,KAAK;IACZ,OAAO;AACT;AAKO,MAAM,oBAAoB;IAC/B,MAAM,QAAQ,IAAI,WAAW;IAC7B,OAAO,eAAe,CAAC;IACvB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAA,OAAQ,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AAC5E;AAKO,MAAM,mBAAmB,CAAC;IAU/B,MAAM,SAAiC,CAAC;IAExC,kBAAkB;IAClB,MAAM,qBAAqB,iBAAiB,SAAS,QAAQ;IAC7D,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAC/B,OAAO,QAAQ,GAAG,mBAAmB,KAAK;IAC5C;IAEA,gCAAgC;IAChC,IAAI,SAAS,QAAQ,EAAE;QACrB,MAAM,qBAAqB,iBAAiB,SAAS,QAAQ;QAC7D,IAAI,CAAC,mBAAmB,OAAO,EAAE;YAC/B,OAAO,QAAQ,GAAG,mBAAmB,KAAK;QAC5C;IACF;IAEA,yBAAyB;IACzB,MAAM,2BAA2B,uBAAuB,SAAS,eAAe;IAChF,IAAI,CAAC,yBAAyB,OAAO,EAAE;QACrC,OAAO,eAAe,GAAG,yBAAyB,KAAK;IACzD;IAEA,6BAA6B;IAC7B,IAAI,SAAS,KAAK,EAAE;QAClB,MAAM,kBAAkB,cAAc,SAAS,KAAK;QACpD,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,OAAO,KAAK,GAAG,gBAAgB,KAAK;QACtC;IACF;IAEA,2BAA2B;IAC3B,IAAI,SAAS,GAAG,EAAE;QAChB,MAAM,gBAAgB,YAAY,SAAS,GAAG;QAC9C,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,OAAO,GAAG,GAAG,cAAc,KAAK;QAClC;IACF;IAEA,+BAA+B;IAC/B,IAAI,SAAS,SAAS,EAAE;QACtB,MAAM,sBAAsB,aAAa,SAAS,SAAS,EAAE;QAC7D,IAAI,CAAC,oBAAoB,OAAO,EAAE;YAChC,OAAO,SAAS,GAAG,oBAAoB,KAAK;QAC9C;IACF;IAEA,IAAI,SAAS,OAAO,EAAE;QACpB,MAAM,oBAAoB,aAAa,SAAS,OAAO,EAAE;QACzD,IAAI,CAAC,kBAAkB,OAAO,EAAE;YAC9B,OAAO,OAAO,GAAG,kBAAkB,KAAK;QAC1C;IACF;IAEA,IAAI,SAAS,mBAAmB,EAAE;QAChC,MAAM,sBAAsB,aAAa,SAAS,mBAAmB,EAAE;QACvE,IAAI,CAAC,oBAAoB,OAAO,EAAE;YAChC,OAAO,mBAAmB,GAAG,oBAAoB,KAAK;QACxD;IACF;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/UserForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { PrimaryButton, SecondaryButton } from '@/components/ui/animated-button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { usersApi } from '@/lib/api'\nimport { User } from '@/types'\nimport { validateUserForm, checkRateLimit } from '@/utils/securityValidation'\nimport { Loader2, Save, X } from 'lucide-react'\n\ninterface UserFormProps {\n  user?: User | null\n  onSave: (user: User) => void\n  onCancel: () => void\n}\n\nexport default function UserForm({ user, onSave, onCancel }: UserFormProps) {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    ruolo: 'user', // L'amministratore può creare solo utenti standard\n    data_scadenza: '',\n    abilitato: true,\n    // Nuovi campi aziendali\n    ragione_sociale: '',\n    indirizzo: '',\n    nazione: '',\n    email: '',\n    vat: '',\n    referente_aziendale: ''\n  })\n  const [errors, setErrors] = useState<Record<string, string>>({})\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  // Inizializza il form con i dati dell'utente se presente\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        username: user.username || '',\n        password: '', // Non mostrare la password esistente\n        ruolo: user.ruolo || 'user',\n        data_scadenza: user.data_scadenza ? user.data_scadenza.split('T')[0] : '',\n        abilitato: user.abilitato !== undefined ? user.abilitato : true,\n        // Nuovi campi aziendali\n        ragione_sociale: user.ragione_sociale || '',\n        indirizzo: user.indirizzo || '',\n        nazione: user.nazione || '',\n        email: user.email || '',\n        vat: user.vat || '',\n        referente_aziendale: user.referente_aziendale || ''\n      })\n    }\n  }, [user])\n\n  // Gestisce il cambio dei valori del form\n  const handleChange = (name: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n    // Rimuovi l'errore per questo campo se presente\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }))\n    }\n  }\n\n  // Validazione del form con controlli di sicurezza\n  const validateForm = () => {\n    // Validazione sicura completa\n    const validation = validateUserForm({\n      username: formData.username,\n      password: user ? undefined : formData.password, // Password obbligatoria solo per nuovi utenti\n      ragione_sociale: formData.ragione_sociale,\n      email: formData.email,\n      vat: formData.vat,\n      indirizzo: formData.indirizzo,\n      nazione: formData.nazione,\n      referente_aziendale: formData.referente_aziendale\n    })\n\n    setErrors(validation.errors)\n    return validation.isValid\n  }\n\n  // Gestisce il submit del form con protezione rate limiting\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    // Rate limiting per prevenire spam/brute force\n    const clientId = `user-form-${user?.id_utente || 'new'}-${Date.now()}`\n    if (!checkRateLimit(clientId, 5, 60000)) { // Max 5 tentativi per minuto\n      setError('Troppi tentativi. Riprova tra un minuto.')\n      return\n    }\n\n    if (!validateForm()) {\n      return\n    }\n\n    setLoading(true)\n    setError('')\n\n    try {\n      // Prepara i dati da inviare\n      const userData = {\n        ...formData\n      }\n\n      // Per nuovi utenti, forza sempre il ruolo \"user\"\n      if (!user) {\n        userData.ruolo = 'user'\n      }\n\n      // Rimuovi la password se è vuota (modifica utente)\n      if (user && !userData.password.trim()) {\n        delete (userData as any).password\n      }\n\n      // Converti la data in formato ISO se presente\n      if (userData.data_scadenza) {\n        userData.data_scadenza = userData.data_scadenza\n      }\n\n      let result\n      if (user) {\n        // Aggiorna l'utente esistente\n        result = await usersApi.updateUser(user.id_utente, userData)\n      } else {\n        // Crea un nuovo utente\n        result = await usersApi.createUser(userData)\n      }\n\n      onSave(result)\n    } catch (err: any) {\n      setError(err.response?.data?.detail || err.message || 'Errore durante il salvataggio dell\\'utente')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>\n          {user ? `Modifica Utente: ${user.username}` : 'Crea Nuovo Utente Standard'}\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\n            <p className=\"text-red-600\">{error}</p>\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* Username */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"username\">Username *</Label>\n              <Input\n                id=\"username\"\n                value={formData.username}\n                onChange={(e) => handleChange('username', e.target.value)}\n                disabled={loading}\n                className={errors.username ? 'border-red-500' : ''}\n              />\n              {errors.username && <p className=\"text-sm text-red-600\">{errors.username}</p>}\n            </div>\n\n            {/* Password */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">\n                {user ? 'Nuova Password (lascia vuoto per non modificare)' : 'Password *'}\n              </Label>\n              <Input\n                id=\"password\"\n                type=\"password\"\n                value={formData.password}\n                onChange={(e) => handleChange('password', e.target.value)}\n                disabled={loading}\n                className={errors.password ? 'border-red-500' : ''}\n              />\n              {errors.password && <p className=\"text-sm text-red-600\">{errors.password}</p>}\n            </div>\n\n            {/* Ragione Sociale */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"ragione_sociale\">Ragione Sociale *</Label>\n              <Input\n                id=\"ragione_sociale\"\n                value={formData.ragione_sociale}\n                onChange={(e) => handleChange('ragione_sociale', e.target.value)}\n                disabled={loading}\n                className={errors.ragione_sociale ? 'border-red-500' : ''}\n              />\n              {errors.ragione_sociale && <p className=\"text-sm text-red-600\">{errors.ragione_sociale}</p>}\n            </div>\n\n            {/* Email */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">Email</Label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                value={formData.email}\n                onChange={(e) => handleChange('email', e.target.value)}\n                disabled={loading}\n                className={errors.email ? 'border-red-500' : ''}\n              />\n              {errors.email && <p className=\"text-sm text-red-600\">{errors.email}</p>}\n            </div>\n\n            {/* Indirizzo */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"indirizzo\">Indirizzo</Label>\n              <Input\n                id=\"indirizzo\"\n                value={formData.indirizzo}\n                onChange={(e) => handleChange('indirizzo', e.target.value)}\n                disabled={loading}\n              />\n            </div>\n\n            {/* Nazione */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"nazione\">Nazione</Label>\n              <Input\n                id=\"nazione\"\n                value={formData.nazione}\n                onChange={(e) => handleChange('nazione', e.target.value)}\n                disabled={loading}\n              />\n            </div>\n\n            {/* VAT */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"vat\">VAT</Label>\n              <Input\n                id=\"vat\"\n                value={formData.vat}\n                onChange={(e) => handleChange('vat', e.target.value)}\n                disabled={loading}\n              />\n            </div>\n\n            {/* Referente Aziendale */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"referente_aziendale\">Referente Aziendale</Label>\n              <Input\n                id=\"referente_aziendale\"\n                value={formData.referente_aziendale}\n                onChange={(e) => handleChange('referente_aziendale', e.target.value)}\n                disabled={loading}\n              />\n            </div>\n\n            {/* Data Scadenza */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"data_scadenza\">Data Scadenza</Label>\n              <Input\n                id=\"data_scadenza\"\n                type=\"date\"\n                value={formData.data_scadenza}\n                onChange={(e) => handleChange('data_scadenza', e.target.value)}\n                disabled={loading}\n              />\n            </div>\n\n            {/* Ruolo - Solo per modifica, per creazione è sempre \"user\" */}\n            {user ? (\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"ruolo\">Ruolo</Label>\n                <Select\n                  value={formData.ruolo}\n                  onValueChange={(value) => handleChange('ruolo', value)}\n                  disabled={loading}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"user\">User</SelectItem>\n                    <SelectItem value=\"cantieri_user\">Cantieri User</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            ) : (\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"ruolo\">Ruolo</Label>\n                <div className=\"px-3 py-2 bg-slate-50 border border-slate-200 rounded-md text-sm text-slate-600\">\n                  User (Standard)\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Utente Abilitato */}\n          <div className=\"flex items-center space-x-2\">\n            <Checkbox\n              id=\"abilitato\"\n              checked={formData.abilitato}\n              onCheckedChange={(checked) => handleChange('abilitato', checked)}\n              disabled={loading || (user && user.ruolo === 'owner')}\n            />\n            <Label htmlFor=\"abilitato\">Utente abilitato</Label>\n          </div>\n\n          {/* Pulsanti */}\n          <div className=\"flex justify-end space-x-4 pt-6\">\n            <SecondaryButton\n              type=\"button\"\n              onClick={onCancel}\n              disabled={loading}\n              icon={<X className=\"h-4 w-4\" />}\n            >\n              Annulla\n            </SecondaryButton>\n            <PrimaryButton\n              type=\"submit\"\n              loading={loading}\n              icon={<Save className=\"h-4 w-4\" />}\n              glow\n            >\n              {loading ? 'Salvataggio...' : 'Salva'}\n            </PrimaryButton>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAbA;;;;;;;;;;;;AAqBe,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAiB;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,UAAU;QACV,OAAO;QACP,eAAe;QACf,WAAW;QACX,wBAAwB;QACxB,iBAAiB;QACjB,WAAW;QACX,SAAS;QACT,OAAO;QACP,KAAK;QACL,qBAAqB;IACvB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,UAAU,KAAK,QAAQ,IAAI;gBAC3B,UAAU;gBACV,OAAO,KAAK,KAAK,IAAI;gBACrB,eAAe,KAAK,aAAa,GAAG,KAAK,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;gBACvE,WAAW,KAAK,SAAS,KAAK,YAAY,KAAK,SAAS,GAAG;gBAC3D,wBAAwB;gBACxB,iBAAiB,KAAK,eAAe,IAAI;gBACzC,WAAW,KAAK,SAAS,IAAI;gBAC7B,SAAS,KAAK,OAAO,IAAI;gBACzB,OAAO,KAAK,KAAK,IAAI;gBACrB,KAAK,KAAK,GAAG,IAAI;gBACjB,qBAAqB,KAAK,mBAAmB,IAAI;YACnD;QACF;IACF,GAAG;QAAC;KAAK;IAET,yCAAyC;IACzC,MAAM,eAAe,CAAC,MAAc;QAClC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QACD,gDAAgD;QAChD,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,kDAAkD;IAClD,MAAM,eAAe;QACnB,8BAA8B;QAC9B,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD,EAAE;YAClC,UAAU,SAAS,QAAQ;YAC3B,UAAU,OAAO,YAAY,SAAS,QAAQ;YAC9C,iBAAiB,SAAS,eAAe;YACzC,OAAO,SAAS,KAAK;YACrB,KAAK,SAAS,GAAG;YACjB,WAAW,SAAS,SAAS;YAC7B,SAAS,SAAS,OAAO;YACzB,qBAAqB,SAAS,mBAAmB;QACnD;QAEA,UAAU,WAAW,MAAM;QAC3B,OAAO,WAAW,OAAO;IAC3B;IAEA,2DAA2D;IAC3D,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,+CAA+C;QAC/C,MAAM,WAAW,CAAC,UAAU,EAAE,MAAM,aAAa,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI;QACtE,IAAI,CAAC,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,GAAG,QAAQ;YACvC,SAAS;YACT;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,4BAA4B;YAC5B,MAAM,WAAW;gBACf,GAAG,QAAQ;YACb;YAEA,iDAAiD;YACjD,IAAI,CAAC,MAAM;gBACT,SAAS,KAAK,GAAG;YACnB;YAEA,mDAAmD;YACnD,IAAI,QAAQ,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;gBACrC,OAAO,AAAC,SAAiB,QAAQ;YACnC;YAEA,8CAA8C;YAC9C,IAAI,SAAS,aAAa,EAAE;gBAC1B,SAAS,aAAa,GAAG,SAAS,aAAa;YACjD;YAEA,IAAI;YACJ,IAAI,MAAM;gBACR,8BAA8B;gBAC9B,SAAS,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;YACrD,OAAO;gBACL,uBAAuB;gBACvB,SAAS,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;YACrC;YAEA,OAAO;QACT,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU,IAAI,OAAO,IAAI;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8BACP,OAAO,CAAC,iBAAiB,EAAE,KAAK,QAAQ,EAAE,GAAG;;;;;;;;;;;0BAGlD,8OAAC,gIAAA,CAAA,cAAW;;oBACT,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;kCAIjC,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;gDACxD,UAAU;gDACV,WAAW,OAAO,QAAQ,GAAG,mBAAmB;;;;;;4CAEjD,OAAO,QAAQ,kBAAI,8OAAC;gDAAE,WAAU;0DAAwB,OAAO,QAAQ;;;;;;;;;;;;kDAI1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DACZ,OAAO,qDAAqD;;;;;;0DAE/D,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;gDACxD,UAAU;gDACV,WAAW,OAAO,QAAQ,GAAG,mBAAmB;;;;;;4CAEjD,OAAO,QAAQ,kBAAI,8OAAC;gDAAE,WAAU;0DAAwB,OAAO,QAAQ;;;;;;;;;;;;kDAI1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,eAAe;gDAC/B,UAAU,CAAC,IAAM,aAAa,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDAC/D,UAAU;gDACV,WAAW,OAAO,eAAe,GAAG,mBAAmB;;;;;;4CAExD,OAAO,eAAe,kBAAI,8OAAC;gDAAE,WAAU;0DAAwB,OAAO,eAAe;;;;;;;;;;;;kDAIxF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK;gDACrD,UAAU;gDACV,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;4CAE9C,OAAO,KAAK,kBAAI,8OAAC;gDAAE,WAAU;0DAAwB,OAAO,KAAK;;;;;;;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,SAAS;gDACzB,UAAU,CAAC,IAAM,aAAa,aAAa,EAAE,MAAM,CAAC,KAAK;gDACzD,UAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAU;;;;;;0DACzB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;gDACvD,UAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAM;;;;;;0DACrB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,GAAG;gDACnB,UAAU,CAAC,IAAM,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;gDACnD,UAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAsB;;;;;;0DACrC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,mBAAmB;gDACnC,UAAU,CAAC,IAAM,aAAa,uBAAuB,EAAE,MAAM,CAAC,KAAK;gDACnE,UAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAC7D,UAAU;;;;;;;;;;;;oCAKb,qBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,KAAK;gDACrB,eAAe,CAAC,QAAU,aAAa,SAAS;gDAChD,UAAU;;kEAEV,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;0EACzB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;6DAKxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,8OAAC;gDAAI,WAAU;0DAAkF;;;;;;;;;;;;;;;;;;0CAQvG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,SAAS,SAAS,SAAS;wCAC3B,iBAAiB,CAAC,UAAY,aAAa,aAAa;wCACxD,UAAU,WAAY,QAAQ,KAAK,KAAK,KAAK;;;;;;kDAE/C,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;;;;;;;0CAI7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,kBAAe;wCACd,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,oBAAM,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;kDACpB;;;;;;kDAGD,8OAAC,8IAAA,CAAA,gBAAa;wCACZ,MAAK;wCACL,SAAS;wCACT,oBAAM,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACtB,IAAI;kDAEH,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 1722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/DatabaseView.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { PrimaryButton } from '@/components/ui/animated-button'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\n\nimport { usersApi } from '@/lib/api'\nimport { Loader2, Database, RefreshCw, Eye } from 'lucide-react'\n\nexport default function DatabaseView() {\n  const [dbData, setDbData] = useState<any>(null)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  // Carica tutti i dati del database\n  const loadDatabaseData = async () => {\n    setLoading(true)\n    setError('')\n\n    try {\n      console.log('Caricamento dati database raw...')\n      const data = await usersApi.getDatabaseData()\n      console.log('Dati ricevuti:', data)\n      setDbData(data)\n    } catch (err: any) {\n      console.error('Errore durante il caricamento:', err)\n      setError(err.response?.data?.detail || err.message || 'Errore durante il caricamento dei dati del database')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    loadDatabaseData()\n  }, [])\n\n  // Renderizza una tabella specifica\n  const renderTable = (tableName: string, tableData: any[], title: string) => {\n    if (!tableData || tableData.length === 0) {\n      return (\n        <div className=\"text-center py-4 text-slate-500 border rounded-lg\">\n          Nessun dato disponibile per {title}\n        </div>\n      )\n    }\n\n    // Ottieni le chiavi per le colonne (dal primo elemento)\n    const columns = Object.keys(tableData[0])\n\n    return (\n      <div className=\"border rounded-lg overflow-hidden mb-6\">\n        <div className=\"bg-slate-100 px-4 py-3 border-b\">\n          <h4 className=\"font-medium text-slate-900\">{title}</h4>\n          <p className=\"text-sm text-slate-600\">Totale record: {tableData.length}</p>\n        </div>\n        <div className=\"overflow-x-auto max-h-96\">\n          <Table>\n            <TableHeader className=\"sticky top-0 bg-slate-50\">\n              <TableRow>\n                {columns.map((column) => (\n                  <TableHead key={column} className=\"font-medium\">\n                    {column}\n                  </TableHead>\n                ))}\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {tableData.map((row, index) => (\n                <TableRow key={index}>\n                  {columns.map((column) => (\n                    <TableCell key={column} className=\"font-mono text-sm\">\n                      {row[column] !== null && row[column] !== undefined\n                        ? String(row[column])\n                        : <span className=\"text-slate-400\">NULL</span>\n                      }\n                    </TableCell>\n                  ))}\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </div>\n      </div>\n    )\n  }\n\n  // Definisce le tabelle disponibili\n  const tables = [\n    { key: 'users', title: 'Utenti', description: 'Tutti gli utenti del sistema' },\n    { key: 'cantieri', title: 'Cantieri', description: 'Tutti i cantieri/progetti' },\n    { key: 'cavi', title: 'Cavi', description: 'Tutti i cavi installati' },\n    { key: 'parco_cavi', title: 'Bobine', description: 'Tutte le bobine del parco cavi' },\n    { key: 'strumenti_certificati', title: 'Strumenti', description: 'Strumenti certificati' },\n    { key: 'certificazioni_cavi', title: 'Certificazioni', description: 'Certificazioni dei cavi' }\n  ]\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <Database className=\"h-5 w-5\" />\n            Visualizzazione Database Raw\n          </CardTitle>\n          <PrimaryButton\n            size=\"sm\"\n            onClick={loadDatabaseData}\n            loading={loading}\n            icon={<RefreshCw className=\"h-4 w-4\" />}\n          >\n            Aggiorna\n          </PrimaryButton>\n        </div>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-start gap-3\">\n            <Eye className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n            <div>\n              <h4 className=\"font-medium text-blue-900\">Visualizzazione Raw del Database</h4>\n              <p className=\"text-sm text-blue-700 mt-1\">\n                Questa sezione mostra i dati grezzi delle tabelle del database. \n                Utile per debugging e analisi dei dati.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {loading ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <Loader2 className=\"h-8 w-8 animate-spin mr-3\" />\n            <span className=\"text-lg\">Caricamento dati database...</span>\n          </div>\n        ) : error ? (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-6\">\n            <p className=\"text-red-600 font-medium\">Errore durante il caricamento:</p>\n            <p className=\"text-red-600\">{error}</p>\n          </div>\n        ) : !dbData ? (\n          <div className=\"text-center py-12 text-slate-500\">\n            Nessun dato disponibile\n          </div>\n        ) : (\n          <div className=\"space-y-8\">\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <p className=\"text-sm text-blue-700\">\n                Visualizzazione completa di tutte le tabelle del database.\n                I dati sono mostrati in formato raw per debugging e analisi.\n              </p>\n            </div>\n\n            {tables.map((table) => (\n              dbData[table.key] && (\n                <div key={table.key}>\n                  <div className=\"mb-4\">\n                    <h3 className=\"text-xl font-semibold text-slate-900\">{table.title}</h3>\n                    <p className=\"text-sm text-slate-600\">{table.description}</p>\n                  </div>\n                  {renderTable(table.key, dbData[table.key], table.title)}\n                </div>\n              )\n            ))}\n\n            {/* Mostra un riepilogo */}\n            <div className=\"bg-slate-50 border border-slate-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-slate-900 mb-2\">Riepilogo Database</h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm\">\n                {tables.map((table) => (\n                  <div key={table.key} className=\"flex justify-between\">\n                    <span className=\"text-slate-600\">{table.title}:</span>\n                    <span className=\"font-medium\">\n                      {dbData[table.key] ? dbData[table.key].length : 0} record\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AATA;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,mCAAmC;IACnC,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,OAAO,MAAM,iHAAA,CAAA,WAAQ,CAAC,eAAe;YAC3C,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,UAAU;QACZ,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU,IAAI,OAAO,IAAI;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,cAAc,CAAC,WAAmB,WAAkB;QACxD,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;YACxC,qBACE,8OAAC;gBAAI,WAAU;;oBAAoD;oBACpC;;;;;;;QAGnC;QAEA,wDAAwD;QACxD,MAAM,UAAU,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE;QAExC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAC5C,8OAAC;4BAAE,WAAU;;gCAAyB;gCAAgB,UAAU,MAAM;;;;;;;;;;;;;8BAExE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0CACJ,8OAAC,iIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC,iIAAA,CAAA,WAAQ;8CACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;4CAAc,WAAU;sDAC/B;2CADa;;;;;;;;;;;;;;;0CAMtB,8OAAC,iIAAA,CAAA,YAAS;0CACP,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC,iIAAA,CAAA,WAAQ;kDACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;gDAAc,WAAU;0DAC/B,GAAG,CAAC,OAAO,KAAK,QAAQ,GAAG,CAAC,OAAO,KAAK,YACrC,OAAO,GAAG,CAAC,OAAO,kBAClB,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;+CAHvB;;;;;uCAFL;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgB7B;IAEA,mCAAmC;IACnC,MAAM,SAAS;QACb;YAAE,KAAK;YAAS,OAAO;YAAU,aAAa;QAA+B;QAC7E;YAAE,KAAK;YAAY,OAAO;YAAY,aAAa;QAA4B;QAC/E;YAAE,KAAK;YAAQ,OAAO;YAAQ,aAAa;QAA0B;QACrE;YAAE,KAAK;YAAc,OAAO;YAAU,aAAa;QAAiC;QACpF;YAAE,KAAK;YAAyB,OAAO;YAAa,aAAa;QAAwB;QACzF;YAAE,KAAK;YAAuB,OAAO;YAAkB,aAAa;QAA0B;KAC/F;IAED,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,8IAAA,CAAA,gBAAa;4BACZ,MAAK;4BACL,SAAS;4BACT,SAAS;4BACT,oBAAM,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;sCAC5B;;;;;;;;;;;;;;;;;0BAKL,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;oBAQ/C,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;+BAE1B,sBACF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;+BAE7B,CAAC,uBACH,8OAAC;wBAAI,WAAU;kCAAmC;;;;;6CAIlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;4BAMtC,OAAO,GAAG,CAAC,CAAC,QACX,MAAM,CAAC,MAAM,GAAG,CAAC,kBACf,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC,MAAM,KAAK;;;;;;8DACjE,8OAAC;oDAAE,WAAU;8DAA0B,MAAM,WAAW;;;;;;;;;;;;wCAEzD,YAAY,MAAM,GAAG,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,KAAK;;mCAL9C,MAAM,GAAG;;;;;0CAWvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDAAK,WAAU;;4DAAkB,MAAM,KAAK;4DAAC;;;;;;;kEAC9C,8OAAC;wDAAK,WAAU;;4DACb,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG;4DAAE;;;;;;;;+CAH5C,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcrC", "debugId": null}}, {"offset": {"line": 2204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/ResetDatabase.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { DangerButton, SecondaryButton } from '@/components/ui/animated-button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { usersApi } from '@/lib/api'\nimport { Loader2, RotateCcw, AlertTriangle, Trash2 } from 'lucide-react'\n\nexport default function ResetDatabase() {\n  const [confirmText, setConfirmText] = useState('')\n  const [confirmChecked, setConfirmChecked] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState('')\n\n  const handleReset = async () => {\n    if (confirmText !== 'RESET DATABASE' || !confirmChecked) {\n      setError('Conferma richiesta per procedere con il reset')\n      return\n    }\n\n    setLoading(true)\n    setError('')\n    setSuccess('')\n\n    try {\n      await usersApi.resetDatabase()\n      setSuccess('Database resettato con successo! Tutti i dati sono stati eliminati.')\n      setConfirmText('')\n      setConfirmChecked(false)\n    } catch (err: any) {\n      setError(err.response?.data?.detail || err.message || 'Errore durante il reset del database')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const isResetEnabled = confirmText === 'RESET DATABASE' && confirmChecked && !loading\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2 text-red-600\">\n          <RotateCcw className=\"h-5 w-5\" />\n          Reset Database\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Avviso di pericolo */}\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-start gap-3\">\n            <AlertTriangle className=\"h-6 w-6 text-red-600 mt-0.5\" />\n            <div>\n              <h4 className=\"font-bold text-red-900 text-lg\">⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE</h4>\n              <div className=\"text-red-700 mt-2 space-y-2\">\n                <p className=\"font-medium\">\n                  Questa operazione eliminerà PERMANENTEMENTE tutti i dati dal database:\n                </p>\n                <ul className=\"list-disc list-inside space-y-1 text-sm\">\n                  <li>Tutti gli utenti (eccetto l'amministratore principale)</li>\n                  <li>Tutti i cantieri e i progetti</li>\n                  <li>Tutti i cavi installati</li>\n                  <li>Tutte le bobine del parco cavi</li>\n                  <li>Tutti i comandi e le certificazioni</li>\n                  <li>Tutti i report e i dati di produttività</li>\n                </ul>\n                <p className=\"font-bold text-red-800 mt-3\">\n                  NON È POSSIBILE RECUPERARE I DATI DOPO IL RESET!\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Messaggi di stato */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n            <p className=\"text-red-600\">{error}</p>\n          </div>\n        )}\n\n        {success && (\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n            <p className=\"text-green-600\">{success}</p>\n          </div>\n        )}\n\n        {/* Form di conferma */}\n        <div className=\"space-y-4 border-t pt-6\">\n          <div>\n            <h4 className=\"font-semibold text-slate-900 mb-4\">\n              Conferma Reset Database\n            </h4>\n            <p className=\"text-sm text-slate-600 mb-4\">\n              Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:\n            </p>\n          </div>\n\n          <div className=\"space-y-4\">\n            {/* Step 1: Digitare testo di conferma */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"confirm-text\" className=\"text-sm font-medium\">\n                1. Digita esattamente: <code className=\"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold\">RESET DATABASE</code>\n              </Label>\n              <Input\n                id=\"confirm-text\"\n                value={confirmText}\n                onChange={(e) => setConfirmText(e.target.value)}\n                placeholder=\"Digita: RESET DATABASE\"\n                disabled={loading}\n                className={confirmText === 'RESET DATABASE' ? 'border-green-500' : ''}\n              />\n            </div>\n\n            {/* Step 2: Checkbox di conferma */}\n            <div className=\"flex items-start space-x-3\">\n              <Checkbox\n                id=\"confirm-checkbox\"\n                checked={confirmChecked}\n                onCheckedChange={setConfirmChecked}\n                disabled={loading}\n              />\n              <Label htmlFor=\"confirm-checkbox\" className=\"text-sm leading-relaxed\">\n                2. Confermo di aver compreso che questa operazione eliminerà TUTTI i dati dal database \n                in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario.\n              </Label>\n            </div>\n          </div>\n\n          {/* Stato di conferma */}\n          <div className=\"bg-slate-50 border border-slate-200 rounded-lg p-4\">\n            <h5 className=\"font-medium text-slate-900 mb-2\">Stato Conferma:</h5>\n            <div className=\"space-y-1 text-sm\">\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-3 h-3 rounded-full ${\n                  confirmText === 'RESET DATABASE' ? 'bg-green-500' : 'bg-red-500'\n                }`}></div>\n                <span>Testo di conferma: {confirmText === 'RESET DATABASE' ? '✓ Corretto' : '✗ Richiesto'}</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-3 h-3 rounded-full ${\n                  confirmChecked ? 'bg-green-500' : 'bg-red-500'\n                }`}></div>\n                <span>Checkbox confermata: {confirmChecked ? '✓ Sì' : '✗ Richiesta'}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Pulsante di reset */}\n          <DangerButton\n            onClick={handleReset}\n            disabled={!isResetEnabled}\n            className=\"w-full\"\n            size=\"lg\"\n            loading={loading}\n            icon={<Trash2 className=\"h-5 w-5\" />}\n            glow\n          >\n            {loading ? 'Reset in corso...' : 'RESET DATABASE - ELIMINA TUTTI I DATI'}\n          </DangerButton>\n\n          {!isResetEnabled && (\n            <p className=\"text-center text-sm text-slate-500\">\n              Completa tutti i passaggi di conferma per abilitare il reset\n            </p>\n          )}\n        </div>\n\n        {/* Informazioni aggiuntive */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm\">\n          <h5 className=\"font-medium text-blue-900 mb-2\">Informazioni Tecniche:</h5>\n          <ul className=\"text-blue-700 space-y-1\">\n            <li>• Il reset manterrà la struttura delle tabelle</li>\n            <li>• L'utente amministratore principale verrà ricreato</li>\n            <li>• Le configurazioni di sistema verranno ripristinate ai valori di default</li>\n            <li>• L'operazione può richiedere alcuni minuti per completarsi</li>\n          </ul>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAVA;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,cAAc;QAClB,IAAI,gBAAgB,oBAAoB,CAAC,gBAAgB;YACvD,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,iHAAA,CAAA,WAAQ,CAAC,aAAa;YAC5B,WAAW;YACX,eAAe;YACf,kBAAkB;QACpB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU,IAAI,OAAO,IAAI;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,gBAAgB,oBAAoB,kBAAkB,CAAC;IAE9E,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIrC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAG3B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;8DAEN,8OAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASlD,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;oBAIhC,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;kCAKnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAGlD,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;;;;;;;0CAK7C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAe,WAAU;;oDAAsB;kEACrC,8OAAC;wDAAK,WAAU;kEAAwD;;;;;;;;;;;;0DAEjG,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,aAAY;gDACZ,UAAU;gDACV,WAAW,gBAAgB,mBAAmB,qBAAqB;;;;;;;;;;;;kDAKvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,SAAS;gDACT,iBAAiB;gDACjB,UAAU;;;;;;0DAEZ,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAmB,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;0CAQ1E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,gBAAgB,mBAAmB,iBAAiB,cACpD;;;;;;kEACF,8OAAC;;4DAAK;4DAAoB,gBAAgB,mBAAmB,eAAe;;;;;;;;;;;;;0DAE9E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,iBAAiB,iBAAiB,cAClC;;;;;;kEACF,8OAAC;;4DAAK;4DAAsB,iBAAiB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;0CAM5D,8OAAC,8IAAA,CAAA,eAAY;gCACX,SAAS;gCACT,UAAU,CAAC;gCACX,WAAU;gCACV,MAAK;gCACL,SAAS;gCACT,oBAAM,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCACxB,IAAI;0CAEH,UAAU,sBAAsB;;;;;;4BAGlC,CAAC,gCACA,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;kCAOtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 2716, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/TipologieCaviManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Cable, Building, FileText, Tag, Plus } from 'lucide-react'\n\nexport default function TipologieCaviManager() {\n  const [activeTab, setActiveTab] = useState('categorie')\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Cable className=\"h-5 w-5\" />\n          Database Tipologie Cavi\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-start gap-3\">\n            <Cable className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n            <div>\n              <h4 className=\"font-medium text-blue-900\">Database Enciclopedico Tipologie Cavi</h4>\n              <p className=\"text-sm text-blue-700 mt-1\">\n                Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, \n                standard e tipologie specifiche. Questo database serve come riferimento per \n                la classificazione e gestione dei cavi nei progetti.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-4\">\n            <TabsTrigger value=\"categorie\" className=\"flex items-center gap-2\">\n              <Tag className=\"h-4 w-4\" />\n              Categorie\n            </TabsTrigger>\n            <TabsTrigger value=\"produttori\" className=\"flex items-center gap-2\">\n              <Building className=\"h-4 w-4\" />\n              Produttori\n            </TabsTrigger>\n            <TabsTrigger value=\"standard\" className=\"flex items-center gap-2\">\n              <FileText className=\"h-4 w-4\" />\n              Standard\n            </TabsTrigger>\n            <TabsTrigger value=\"tipologie\" className=\"flex items-center gap-2\">\n              <Cable className=\"h-4 w-4\" />\n              Tipologie\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"categorie\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Categorie Cavi</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuova Categoria\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <Tag className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione categorie cavi - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile creare, modificare ed eliminare le categorie di cavi\n              </p>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"produttori\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Produttori</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuovo Produttore\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <Building className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione produttori - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile gestire l'anagrafica dei produttori di cavi\n              </p>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"standard\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Standard e Normative</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuovo Standard\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <FileText className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione standard - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile gestire gli standard tecnici e le normative di riferimento\n              </p>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"tipologie\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Tipologie Specifiche</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuova Tipologia\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <Cable className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione tipologie - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate\n              </p>\n            </div>\n          </TabsContent>\n        </Tabs>\n\n        {/* Informazioni aggiuntive */}\n        <div className=\"bg-slate-50 border border-slate-200 rounded-lg p-4\">\n          <h5 className=\"font-medium text-slate-900 mb-2\">Struttura Database Tipologie:</h5>\n          <div className=\"text-sm text-slate-600 space-y-1\">\n            <p><strong>Categorie:</strong> Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)</p>\n            <p><strong>Produttori:</strong> Aziende produttrici con informazioni di contatto</p>\n            <p><strong>Standard:</strong> Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)</p>\n            <p><strong>Tipologie:</strong> Specifiche tecniche dettagliate per ogni tipo di cavo</p>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIjC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;kCAShD,8OAAC,gIAAA,CAAA,OAAI;wBAAC,OAAO;wBAAW,eAAe;wBAAc,WAAU;;0CAC7D,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG7B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAa,WAAU;;0DACxC,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;;0DACtC,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAKjC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;;kDACvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,8OAAC,kIAAA,CAAA,SAAM;;kEACL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;0DAAE;;;;;;0DACH,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAMhC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAa,WAAU;;kDACxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,8OAAC,kIAAA,CAAA,SAAM;;kEACL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAE;;;;;;0DACH,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAMhC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDACtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,8OAAC,kIAAA,CAAA,SAAM;;kEACL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAE;;;;;;0DACH,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAMhC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;;kDACvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,8OAAC,kIAAA,CAAA,SAAM;;kEACL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAE;;;;;;0DACH,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;;;;;;;kCAQlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DAAE,8OAAC;0DAAO;;;;;;4CAAmB;;;;;;;kDAC9B,8OAAC;;0DAAE,8OAAC;0DAAO;;;;;;4CAAoB;;;;;;;kDAC/B,8OAAC;;0DAAE,8OAAC;0DAAO;;;;;;4CAAkB;;;;;;;kDAC7B,8OAAC;;0DAAE,8OAAC;0DAAO;;;;;;4CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1C", "debugId": null}}, {"offset": {"line": 3374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { AnimatedButton, PrimaryButton, SecondaryButton, DangerButton, OutlineButton, QuickButton } from '@/components/ui/animated-button'\nimport CompactActionsDropdown from '@/components/ui/compact-actions-dropdown'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { api, cantieriApi, usersApi } from '@/lib/api'\nimport { User, Cantiere } from '@/types'\nimport UserForm from '@/components/admin/UserForm'\nimport DatabaseView from '@/components/admin/DatabaseView'\nimport ResetDatabase from '@/components/admin/ResetDatabase'\nimport TipologieCaviManager from '@/components/admin/TipologieCaviManager'\nimport {\n  Settings,\n  Users,\n  Building2,\n  Search,\n  Plus,\n  Edit,\n  Trash2,\n  CheckCircle,\n  Clock,\n  AlertCircle,\n  Eye,\n  Shield,\n  Key,\n  Loader2,\n  UserPlus,\n  LogIn,\n  Cable,\n  Database,\n  RotateCcw,\n  RefreshCw\n} from 'lucide-react'\n\nexport default function AdminPage() {\n  const router = useRouter()\n  const [activeTab, setActiveTab] = useState('visualizza-utenti')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [users, setUsers] = useState<User[]>([])\n  const [cantieri, setCantieri] = useState<Cantiere[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [selectedUser, setSelectedUser] = useState<User | null>(null)\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' })\n\n  const { user, impersonateUser } = useAuth()\n\n  // Carica dati dal backend\n  useEffect(() => {\n    loadData()\n  }, [activeTab])\n\n  const loadData = async () => {\n    try {\n      setIsLoading(true)\n      setError('')\n\n      console.log('Caricamento dati per tab:', activeTab)\n      console.log('Token presente:', typeof window !== 'undefined' ? localStorage.getItem('token') : 'N/A')\n      console.log('Utente corrente:', user)\n\n      if (activeTab === 'visualizza-utenti' || activeTab === 'crea-utente' || activeTab === 'accedi-come-utente') {\n        console.log('Chiamata API per ottenere utenti...')\n        const usersData = await usersApi.getUsers()\n        console.log('Utenti ricevuti:', usersData)\n        setUsers(usersData)\n      } else if (activeTab === 'cantieri') {\n        const cantieriData = await cantieriApi.getCantieri()\n        setCantieri(cantieriData)\n      }\n    } catch (error: any) {\n      console.error('Errore caricamento dati:', error)\n      console.error('Dettagli errore:', error.response)\n      setError(error.response?.data?.detail || error.message || 'Errore durante il caricamento dei dati')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleEditUser = (userToEdit: User) => {\n    setSelectedUser(userToEdit)\n    setActiveTab('modifica-utente')\n  }\n\n  const handleToggleUserStatus = async (userId: number) => {\n    try {\n      await usersApi.toggleUserStatus(userId)\n      loadData() // Ricarica i dati\n    } catch (error: any) {\n      console.error('Errore toggle status:', error)\n      setError(error.response?.data?.detail || 'Errore durante la modifica dello stato utente')\n    }\n  }\n\n  const handleDeleteUser = async (userId: number) => {\n    if (confirm('Sei sicuro di voler eliminare questo utente?')) {\n      try {\n        await usersApi.deleteUser(userId)\n        loadData() // Ricarica i dati\n      } catch (error: any) {\n        console.error('Errore eliminazione utente:', error)\n        setError(error.response?.data?.detail || 'Errore durante l\\'eliminazione dell\\'utente')\n      }\n    }\n  }\n\n  const handleSaveUser = (savedUser: User) => {\n    console.log('Utente salvato:', savedUser)\n    setSelectedUser(null)\n    setActiveTab('visualizza-utenti')\n    loadData() // Ricarica i dati\n  }\n\n  const handleCancelForm = () => {\n    setSelectedUser(null)\n    setActiveTab('visualizza-utenti')\n  }\n\n  const handleQuickImpersonate = async (targetUser: User) => {\n    try {\n      console.log('Impersonificazione rapida utente:', targetUser.username, 'Ruolo:', targetUser.ruolo)\n\n      await impersonateUser(targetUser.id_utente)\n\n      // Reindirizza in base al ruolo dell'utente impersonato\n      if (targetUser.ruolo === 'user') {\n        router.push('/cantieri')\n      } else if (targetUser.ruolo === 'cantieri_user') {\n        router.push('/cavi')\n      } else {\n        router.push('/')\n      }\n    } catch (error: any) {\n      console.error('Errore durante l\\'impersonificazione rapida:', error)\n      setError(error.response?.data?.detail || error.message || 'Errore durante l\\'impersonificazione')\n    }\n  }\n\n  // Helper functions per i badge\n\n  const getRuoloBadge = (ruolo: string) => {\n    switch (ruolo) {\n      case 'owner':\n        return <Badge className=\"bg-purple-100 text-purple-800\">Owner</Badge>\n      case 'user':\n        return <Badge className=\"bg-blue-100 text-blue-800\">User</Badge>\n      case 'cantieri_user':\n        return <Badge className=\"bg-green-100 text-green-800\">Cantieri User</Badge>\n      default:\n        return <Badge variant=\"secondary\">{ruolo}</Badge>\n    }\n  }\n\n  const getStatusBadge = (abilitato: boolean, data_scadenza?: string) => {\n    if (!abilitato) {\n      return <Badge className=\"bg-red-100 text-red-800\">Disabilitato</Badge>\n    }\n    \n    if (data_scadenza) {\n      const scadenza = new Date(data_scadenza)\n      const oggi = new Date()\n      \n      if (scadenza < oggi) {\n        return <Badge className=\"bg-red-100 text-red-800\">Scaduto</Badge>\n      } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {\n        return <Badge className=\"bg-yellow-100 text-yellow-800\">In Scadenza</Badge>\n      }\n    }\n    \n    return <Badge className=\"bg-green-100 text-green-800\">Attivo</Badge>\n  }\n\n  const filteredUsers = users.filter(u =>\n    u.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    u.ragione_sociale?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    u.email?.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  // Verifica se l'utente ha permessi di amministrazione\n  // Durante il logout, user diventa null, quindi reindirizza direttamente al login\n  if (!user) {\n    if (typeof window !== 'undefined') {\n      window.location.replace('/login')\n    }\n    return null\n  }\n\n  if (user.ruolo !== 'owner') {\n    if (typeof window !== 'undefined') {\n      window.location.replace('/login')\n    }\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-[90%] mx-auto space-y-6\">\n\n\n\n        {/* Tabs - Stile sottile senza hover invasivi */}\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className={`grid w-full ${selectedUser ? 'grid-cols-6' : 'grid-cols-5'}`}>\n            <TabsTrigger value=\"visualizza-utenti\" className=\"tab-trigger flex items-center gap-2\">\n              <Users className=\"h-4 w-4\" />\n              Visualizza Utenti\n            </TabsTrigger>\n            <TabsTrigger value=\"crea-utente\" className=\"tab-trigger flex items-center gap-2\">\n              <UserPlus className=\"h-4 w-4\" />\n              Crea Nuovo Utente\n            </TabsTrigger>\n            {selectedUser && (\n              <TabsTrigger value=\"modifica-utente\" className=\"tab-trigger flex items-center gap-2\">\n                <Edit className=\"h-4 w-4\" />\n                Modifica Utente\n              </TabsTrigger>\n            )}\n            <TabsTrigger value=\"database-tipologie-cavi\" className=\"tab-trigger flex items-center gap-2\">\n              <Cable className=\"h-4 w-4\" />\n              Database Tipologie Cavi\n            </TabsTrigger>\n            <TabsTrigger value=\"visualizza-database-raw\" className=\"tab-trigger flex items-center gap-2\">\n              <Database className=\"h-4 w-4\" />\n              Visualizza Database Raw\n            </TabsTrigger>\n            <TabsTrigger value=\"reset-database\" className=\"tab-trigger flex items-center gap-2\">\n              <RotateCcw className=\"h-4 w-4\" />\n              Reset Database\n            </TabsTrigger>\n          </TabsList>\n\n          {/* Tab Visualizza Utenti */}\n          <TabsContent value=\"visualizza-utenti\" className=\"space-y-4\">\n\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                <p className=\"text-red-600\">{error}</p>\n              </div>\n            )}\n\n            <Card>\n              <CardHeader>\n                <CardTitle>Lista Utenti</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"overflow-x-auto\">\n                  <div className=\"rounded-md border\">\n                    <Table className=\"min-w-full\">\n                    <TableHeader>\n                      <TableRow>\n                        <TableHead className=\"w-[60px] text-center\">ID</TableHead>\n                        <TableHead className=\"w-[120px]\">Username</TableHead>\n                        <TableHead className=\"w-[100px] text-center\">Password</TableHead>\n                        <TableHead className=\"w-[100px] text-center\">Ruolo</TableHead>\n                        <TableHead className=\"w-[250px]\">Ragione Sociale</TableHead>\n                        <TableHead className=\"w-[200px]\">Email</TableHead>\n                        <TableHead className=\"w-[120px] text-center\">VAT</TableHead>\n                        <TableHead className=\"w-[100px] text-center\">Nazione</TableHead>\n                        <TableHead className=\"w-[150px]\">Referente</TableHead>\n                        <TableHead className=\"w-[100px] text-center\">Scadenza</TableHead>\n                        <TableHead className=\"w-[100px] text-center\">Stato</TableHead>\n                        <TableHead className=\"w-[120px] text-center\">Azioni</TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {isLoading ? (\n                        <TableRow>\n                          <TableCell colSpan={12} className=\"text-center py-8\">\n                            <div className=\"flex items-center justify-center gap-2\">\n                              <Loader2 className=\"h-4 w-4 animate-spin\" />\n                              Caricamento...\n                            </div>\n                          </TableCell>\n                        </TableRow>\n                      ) : users.length === 0 ? (\n                        <TableRow>\n                          <TableCell colSpan={12} className=\"text-center py-8 text-slate-500\">\n                            Nessun utente trovato\n                          </TableCell>\n                        </TableRow>\n                      ) : (\n                        users.map((utente) => (\n                          <TableRow key={utente.id_utente} className=\"hover:bg-slate-50\">\n                            {/* ID */}\n                            <TableCell className=\"text-center text-slate-500 text-sm font-mono\">\n                              {utente.id_utente}\n                            </TableCell>\n\n                            {/* Username */}\n                            <TableCell className=\"font-semibold text-slate-900\">\n                              {utente.username}\n                            </TableCell>\n\n                            {/* Password */}\n                            <TableCell className=\"text-center font-mono text-xs text-slate-500\">\n                              {utente.password_plain || '***'}\n                            </TableCell>\n\n                            {/* Ruolo */}\n                            <TableCell className=\"text-center\">\n                              {getRuoloBadge(utente.ruolo)}\n                            </TableCell>\n\n                            {/* Ragione Sociale */}\n                            <TableCell className=\"max-w-[250px] truncate\" title={utente.ragione_sociale}>\n                              <span className=\"text-slate-900\">{utente.ragione_sociale || '-'}</span>\n                            </TableCell>\n\n                            {/* Email */}\n                            <TableCell className=\"max-w-[200px] truncate text-sm text-slate-600\" title={utente.email}>\n                              {utente.email || '-'}\n                            </TableCell>\n\n                            {/* VAT */}\n                            <TableCell className=\"text-center text-sm text-slate-600\">\n                              {utente.vat || '-'}\n                            </TableCell>\n\n                            {/* Nazione */}\n                            <TableCell className=\"text-center text-sm text-slate-600\">\n                              {utente.nazione || '-'}\n                            </TableCell>\n\n                            {/* Referente */}\n                            <TableCell className=\"max-w-[150px] truncate text-sm text-slate-600\" title={utente.referente_aziendale}>\n                              {utente.referente_aziendale || '-'}\n                            </TableCell>\n\n                            {/* Scadenza */}\n                            <TableCell className=\"text-center text-sm text-slate-600\">\n                              {utente.data_scadenza ?\n                                new Date(utente.data_scadenza).toLocaleDateString('it-IT') :\n                                'N/A'\n                              }\n                            </TableCell>\n\n                            {/* Stato */}\n                            <TableCell className=\"text-center\">\n                              {getStatusBadge(utente.abilitato, utente.data_scadenza)}\n                            </TableCell>\n\n                            {/* Azioni */}\n                            <TableCell className=\"text-center\">\n                              <div className=\"flex items-center justify-center gap-2\">\n                                {/* Dropdown compatto con triangolino */}\n                                <CompactActionsDropdown\n                                  user={utente}\n                                  onEdit={() => handleEditUser(utente)}\n                                  onToggleStatus={() => handleToggleUserStatus(utente.id_utente)}\n                                  onDelete={() => handleDeleteUser(utente.id_utente)}\n                                />\n\n                                {/* Pulsante Entra */}\n                                <PrimaryButton\n                                  size=\"sm\"\n                                  onClick={() => handleQuickImpersonate(utente)}\n                                  disabled={utente.ruolo === 'owner' || !utente.abilitato}\n                                  className=\"px-3 py-1.5 text-xs\"\n                                  icon={<LogIn className=\"h-3.5 w-3.5\" />}\n                                >\n                                  Entra\n                                </PrimaryButton>\n                              </div>\n                            </TableCell>\n                          </TableRow>\n                        ))\n                      )}\n                    </TableBody>\n                    </Table>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Tab Crea Nuovo Utente */}\n          <TabsContent value=\"crea-utente\" className=\"space-y-4\">\n            <UserForm\n              user={null}\n              onSave={handleSaveUser}\n              onCancel={handleCancelForm}\n            />\n          </TabsContent>\n\n          {/* Tab Modifica Utente - Visibile solo quando un utente è selezionato */}\n          {selectedUser && (\n            <TabsContent value=\"modifica-utente\" className=\"space-y-4\">\n              <UserForm\n                user={selectedUser}\n                onSave={handleSaveUser}\n                onCancel={handleCancelForm}\n              />\n            </TabsContent>\n          )}\n\n\n\n          {/* Tab Database Tipologie Cavi */}\n          <TabsContent value=\"database-tipologie-cavi\" className=\"space-y-4\">\n            <TipologieCaviManager />\n          </TabsContent>\n\n          {/* Tab Visualizza Database Raw */}\n          <TabsContent value=\"visualizza-database-raw\" className=\"space-y-4\">\n            <DatabaseView />\n          </TabsContent>\n\n          {/* Tab Reset Database */}\n          <TabsContent value=\"reset-database\" className=\"space-y-4\">\n            <ResetDatabase />\n          </TabsContent>\n\n        </Tabs>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAnBA;;;;;;;;;;;;;;;;;AA0Ce,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAO,SAAS;QAAI,UAAU;IAAU;IAEjG,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAExC,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,WAAW;QACf,IAAI;YACF,aAAa;YACb,SAAS;YAET,QAAQ,GAAG,CAAC,6BAA6B;YACzC,QAAQ,GAAG,CAAC,mBAAmB,6EAAgE;YAC/F,QAAQ,GAAG,CAAC,oBAAoB;YAEhC,IAAI,cAAc,uBAAuB,cAAc,iBAAiB,cAAc,sBAAsB;gBAC1G,QAAQ,GAAG,CAAC;gBACZ,MAAM,YAAY,MAAM,iHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACzC,QAAQ,GAAG,CAAC,oBAAoB;gBAChC,SAAS;YACX,OAAO,IAAI,cAAc,YAAY;gBACnC,MAAM,eAAe,MAAM,iHAAA,CAAA,cAAW,CAAC,WAAW;gBAClD,YAAY;YACd;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ;YAChD,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;QAC5D,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,iHAAA,CAAA,WAAQ,CAAC,gBAAgB,CAAC;YAChC,WAAW,kBAAkB;;QAC/B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,QAAQ,iDAAiD;YAC3D,IAAI;gBACF,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;gBAC1B,WAAW,kBAAkB;;YAC/B,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;YAC3C;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,gBAAgB;QAChB,aAAa;QACb,WAAW,kBAAkB;;IAC/B;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,QAAQ,GAAG,CAAC,qCAAqC,WAAW,QAAQ,EAAE,UAAU,WAAW,KAAK;YAEhG,MAAM,gBAAgB,WAAW,SAAS;YAE1C,uDAAuD;YACvD,IAAI,WAAW,KAAK,KAAK,QAAQ;gBAC/B,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,WAAW,KAAK,KAAK,iBAAiB;gBAC/C,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;QAC5D;IACF;IAEA,+BAA+B;IAE/B,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,MAAM,iBAAiB,CAAC,WAAoB;QAC1C,IAAI,CAAC,WAAW;YACd,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA0B;;;;;;QACpD;QAEA,IAAI,eAAe;YACjB,MAAM,WAAW,IAAI,KAAK;YAC1B,MAAM,OAAO,IAAI;YAEjB,IAAI,WAAW,MAAM;gBACnB,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA0B;;;;;;YACpD,OAAO,IAAI,SAAS,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM;gBACxE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D;QACF;QAEA,qBAAO,8OAAC,iIAAA,CAAA,QAAK;YAAC,WAAU;sBAA8B;;;;;;IACxD;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,IACjC,EAAE,QAAQ,EAAE,cAAc,SAAS,WAAW,WAAW,OACzD,EAAE,eAAe,EAAE,cAAc,SAAS,WAAW,WAAW,OAChE,EAAE,KAAK,EAAE,cAAc,SAAS,WAAW,WAAW;IAGxD,sDAAsD;IACtD,iFAAiF;IACjF,IAAI,CAAC,MAAM;QACT,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEA,IAAI,KAAK,KAAK,KAAK,SAAS;QAC1B,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBAKb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;gBAAc,WAAU;;kCAC7D,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAW,CAAC,YAAY,EAAE,eAAe,gBAAgB,eAAe;;0CAChF,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAoB,WAAU;;kDAC/C,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAc,WAAU;;kDACzC,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;4BAGjC,8BACC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAkB,WAAU;;kDAC7C,8OAAC,2MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAIhC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAA0B,WAAU;;kDACrD,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAA0B,WAAU;;kDACrD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAiB,WAAU;;kDAC5C,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAMrC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAoB,WAAU;;4BAE9C,uBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;0CAIjC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;sEACjB,8OAAC,iIAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;kFACP,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAuB;;;;;;kFAC5C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAY;;;;;;kFACjC,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAwB;;;;;;kFAC7C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAwB;;;;;;kFAC7C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAY;;;;;;kFACjC,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAY;;;;;;kFACjC,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAwB;;;;;;kFAC7C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAwB;;;;;;kFAC7C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAY;;;;;;kFACjC,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAwB;;;;;;kFAC7C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAwB;;;;;;kFAC7C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAwB;;;;;;;;;;;;;;;;;sEAGjD,8OAAC,iIAAA,CAAA,YAAS;sEACP,0BACC,8OAAC,iIAAA,CAAA,WAAQ;0EACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;oEAAC,SAAS;oEAAI,WAAU;8EAChC,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iNAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAAyB;;;;;;;;;;;;;;;;uEAKhD,MAAM,MAAM,KAAK,kBACnB,8OAAC,iIAAA,CAAA,WAAQ;0EACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;oEAAC,SAAS;oEAAI,WAAU;8EAAkC;;;;;;;;;;uEAKtE,MAAM,GAAG,CAAC,CAAC,uBACT,8OAAC,iIAAA,CAAA,WAAQ;oEAAwB,WAAU;;sFAEzC,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,OAAO,SAAS;;;;;;sFAInB,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,OAAO,QAAQ;;;;;;sFAIlB,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,OAAO,cAAc,IAAI;;;;;;sFAI5B,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,cAAc,OAAO,KAAK;;;;;;sFAI7B,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;4EAAyB,OAAO,OAAO,eAAe;sFACzE,cAAA,8OAAC;gFAAK,WAAU;0FAAkB,OAAO,eAAe,IAAI;;;;;;;;;;;sFAI9D,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;4EAAgD,OAAO,OAAO,KAAK;sFACrF,OAAO,KAAK,IAAI;;;;;;sFAInB,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,OAAO,GAAG,IAAI;;;;;;sFAIjB,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,OAAO,OAAO,IAAI;;;;;;sFAIrB,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;4EAAgD,OAAO,OAAO,mBAAmB;sFACnG,OAAO,mBAAmB,IAAI;;;;;;sFAIjC,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,OAAO,aAAa,GACnB,IAAI,KAAK,OAAO,aAAa,EAAE,kBAAkB,CAAC,WAClD;;;;;;sFAKJ,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,eAAe,OAAO,SAAS,EAAE,OAAO,aAAa;;;;;;sFAIxD,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFACnB,cAAA,8OAAC;gFAAI,WAAU;;kGAEb,8OAAC,0JAAA,CAAA,UAAsB;wFACrB,MAAM;wFACN,QAAQ,IAAM,eAAe;wFAC7B,gBAAgB,IAAM,uBAAuB,OAAO,SAAS;wFAC7D,UAAU,IAAM,iBAAiB,OAAO,SAAS;;;;;;kGAInD,8OAAC,8IAAA,CAAA,gBAAa;wFACZ,MAAK;wFACL,SAAS,IAAM,uBAAuB;wFACtC,UAAU,OAAO,KAAK,KAAK,WAAW,CAAC,OAAO,SAAS;wFACvD,WAAU;wFACV,oBAAM,8OAAC,wMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;kGACxB;;;;;;;;;;;;;;;;;;mEA7EQ,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA8F/C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAc,WAAU;kCACzC,cAAA,8OAAC,uIAAA,CAAA,UAAQ;4BACP,MAAM;4BACN,QAAQ;4BACR,UAAU;;;;;;;;;;;oBAKb,8BACC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAkB,WAAU;kCAC7C,cAAA,8OAAC,uIAAA,CAAA,UAAQ;4BACP,MAAM;4BACN,QAAQ;4BACR,UAAU;;;;;;;;;;;kCAQhB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAA0B,WAAU;kCACrD,cAAA,8OAAC,mJAAA,CAAA,UAAoB;;;;;;;;;;kCAIvB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAA0B,WAAU;kCACrD,cAAA,8OAAC,2IAAA,CAAA,UAAY;;;;;;;;;;kCAIf,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAiB,WAAU;kCAC5C,cAAA,8OAAC,4IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B", "debugId": null}}]}