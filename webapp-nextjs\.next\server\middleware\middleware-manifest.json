{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "DmFlnj8I3P1ljkWGhQVgM+UNdCE3hT3/mJhLA6xJySg=", "__NEXT_PREVIEW_MODE_ID": "fc63a83f3155f5ec0c96fc25ab8fd9f2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a0b0eae0cedd2999ee32f237ad2c633a352e3183a773cd120ef03c5660ea3fe1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ee68efc682516563ff6fab2d4a93092fe6ddb58eef2f5cb93d1bfb67c33a1302"}}}, "instrumentation": null, "functions": {}}