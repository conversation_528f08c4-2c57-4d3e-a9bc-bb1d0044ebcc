{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/UserForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { PrimaryButton, SecondaryButton } from '@/components/ui/animated-button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { usersApi } from '@/lib/api'\nimport { User } from '@/types'\nimport { Loader2, Save, X } from 'lucide-react'\n\ninterface UserFormProps {\n  user?: User | null\n  onSave: (user: User) => void\n  onCancel: () => void\n}\n\nexport default function UserForm({ user, onSave, onCancel }: UserFormProps) {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    ruolo: 'user', // L'amministratore può creare solo utenti standard\n    data_scadenza: '',\n    abilitato: true,\n    // Nuovi campi aziendali\n    ragione_sociale: '',\n    indirizzo: '',\n    nazione: '',\n    email: '',\n    vat: '',\n    referente_aziendale: ''\n  })\n  const [errors, setErrors] = useState<Record<string, string>>({})\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  // Inizializza il form con i dati dell'utente se presente\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        username: user.username || '',\n        password: '', // Non mostrare la password esistente\n        ruolo: user.ruolo || 'user',\n        data_scadenza: user.data_scadenza ? user.data_scadenza.split('T')[0] : '',\n        abilitato: user.abilitato !== undefined ? user.abilitato : true,\n        // Nuovi campi aziendali\n        ragione_sociale: user.ragione_sociale || '',\n        indirizzo: user.indirizzo || '',\n        nazione: user.nazione || '',\n        email: user.email || '',\n        vat: user.vat || '',\n        referente_aziendale: user.referente_aziendale || ''\n      })\n    }\n  }, [user])\n\n  // Gestisce il cambio dei valori del form\n  const handleChange = (name: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n    // Rimuovi l'errore per questo campo se presente\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }))\n    }\n  }\n\n  // Validazione del form\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username è obbligatorio'\n    }\n\n    if (!user && !formData.password.trim()) {\n      newErrors.password = 'Password è obbligatoria per nuovi utenti'\n    }\n\n    if (!formData.ragione_sociale.trim()) {\n      newErrors.ragione_sociale = 'Ragione sociale è obbligatoria'\n    }\n\n    if (formData.email && !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email non valida'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  // Gestisce il submit del form\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!validateForm()) {\n      return\n    }\n\n    setLoading(true)\n    setError('')\n\n    try {\n      // Prepara i dati da inviare\n      const userData = {\n        ...formData\n      }\n\n      // Rimuovi la password se è vuota (modifica utente)\n      if (user && !userData.password.trim()) {\n        delete (userData as any).password\n      }\n\n      // Converti la data in formato ISO se presente\n      if (userData.data_scadenza) {\n        userData.data_scadenza = userData.data_scadenza\n      }\n\n      let result\n      if (user) {\n        // Aggiorna l'utente esistente\n        result = await usersApi.updateUser(user.id_utente, userData)\n      } else {\n        // Crea un nuovo utente\n        result = await usersApi.createUser(userData)\n      }\n\n      onSave(result)\n    } catch (err: any) {\n      setError(err.response?.data?.detail || err.message || 'Errore durante il salvataggio dell\\'utente')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>\n          {user ? `Modifica Utente: ${user.username}` : 'Crea Nuovo Utente Standard'}\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\n            <p className=\"text-red-600\">{error}</p>\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* Username */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"username\">Username *</Label>\n              <Input\n                id=\"username\"\n                value={formData.username}\n                onChange={(e) => handleChange('username', e.target.value)}\n                disabled={loading}\n                className={errors.username ? 'border-red-500' : ''}\n              />\n              {errors.username && <p className=\"text-sm text-red-600\">{errors.username}</p>}\n            </div>\n\n            {/* Password */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">\n                {user ? 'Nuova Password (lascia vuoto per non modificare)' : 'Password *'}\n              </Label>\n              <Input\n                id=\"password\"\n                type=\"password\"\n                value={formData.password}\n                onChange={(e) => handleChange('password', e.target.value)}\n                disabled={loading}\n                className={errors.password ? 'border-red-500' : ''}\n              />\n              {errors.password && <p className=\"text-sm text-red-600\">{errors.password}</p>}\n            </div>\n\n            {/* Ragione Sociale */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"ragione_sociale\">Ragione Sociale *</Label>\n              <Input\n                id=\"ragione_sociale\"\n                value={formData.ragione_sociale}\n                onChange={(e) => handleChange('ragione_sociale', e.target.value)}\n                disabled={loading}\n                className={errors.ragione_sociale ? 'border-red-500' : ''}\n              />\n              {errors.ragione_sociale && <p className=\"text-sm text-red-600\">{errors.ragione_sociale}</p>}\n            </div>\n\n            {/* Email */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">Email</Label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                value={formData.email}\n                onChange={(e) => handleChange('email', e.target.value)}\n                disabled={loading}\n                className={errors.email ? 'border-red-500' : ''}\n              />\n              {errors.email && <p className=\"text-sm text-red-600\">{errors.email}</p>}\n            </div>\n\n            {/* Indirizzo */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"indirizzo\">Indirizzo</Label>\n              <Input\n                id=\"indirizzo\"\n                value={formData.indirizzo}\n                onChange={(e) => handleChange('indirizzo', e.target.value)}\n                disabled={loading}\n              />\n            </div>\n\n            {/* Nazione */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"nazione\">Nazione</Label>\n              <Input\n                id=\"nazione\"\n                value={formData.nazione}\n                onChange={(e) => handleChange('nazione', e.target.value)}\n                disabled={loading}\n              />\n            </div>\n\n            {/* VAT */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"vat\">VAT</Label>\n              <Input\n                id=\"vat\"\n                value={formData.vat}\n                onChange={(e) => handleChange('vat', e.target.value)}\n                disabled={loading}\n              />\n            </div>\n\n            {/* Referente Aziendale */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"referente_aziendale\">Referente Aziendale</Label>\n              <Input\n                id=\"referente_aziendale\"\n                value={formData.referente_aziendale}\n                onChange={(e) => handleChange('referente_aziendale', e.target.value)}\n                disabled={loading}\n              />\n            </div>\n\n            {/* Data Scadenza */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"data_scadenza\">Data Scadenza</Label>\n              <Input\n                id=\"data_scadenza\"\n                type=\"date\"\n                value={formData.data_scadenza}\n                onChange={(e) => handleChange('data_scadenza', e.target.value)}\n                disabled={loading}\n              />\n            </div>\n\n            {/* Ruolo */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"ruolo\">Ruolo</Label>\n              <Select\n                value={formData.ruolo}\n                onValueChange={(value) => handleChange('ruolo', value)}\n                disabled={loading}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"user\">User</SelectItem>\n                  <SelectItem value=\"cantieri_user\">Cantieri User</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* Utente Abilitato */}\n          <div className=\"flex items-center space-x-2\">\n            <Checkbox\n              id=\"abilitato\"\n              checked={formData.abilitato}\n              onCheckedChange={(checked) => handleChange('abilitato', checked)}\n              disabled={loading || (user && user.ruolo === 'owner')}\n            />\n            <Label htmlFor=\"abilitato\">Utente abilitato</Label>\n          </div>\n\n          {/* Pulsanti */}\n          <div className=\"flex justify-end space-x-4 pt-6\">\n            <SecondaryButton\n              type=\"button\"\n              onClick={onCancel}\n              disabled={loading}\n              icon={<X className=\"h-4 w-4\" />}\n            >\n              Annulla\n            </SecondaryButton>\n            <PrimaryButton\n              type=\"submit\"\n              loading={loading}\n              icon={<Save className=\"h-4 w-4\" />}\n              glow\n            >\n              {loading ? 'Salvataggio...' : 'Salva'}\n            </PrimaryButton>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;;;AAZA;;;;;;;;;;AAoBe,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAiB;;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,UAAU;QACV,OAAO;QACP,eAAe;QACf,WAAW;QACX,wBAAwB;QACxB,iBAAiB;QACjB,WAAW;QACX,SAAS;QACT,OAAO;QACP,KAAK;QACL,qBAAqB;IACvB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,MAAM;gBACR,YAAY;oBACV,UAAU,KAAK,QAAQ,IAAI;oBAC3B,UAAU;oBACV,OAAO,KAAK,KAAK,IAAI;oBACrB,eAAe,KAAK,aAAa,GAAG,KAAK,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;oBACvE,WAAW,KAAK,SAAS,KAAK,YAAY,KAAK,SAAS,GAAG;oBAC3D,wBAAwB;oBACxB,iBAAiB,KAAK,eAAe,IAAI;oBACzC,WAAW,KAAK,SAAS,IAAI;oBAC7B,SAAS,KAAK,OAAO,IAAI;oBACzB,OAAO,KAAK,KAAK,IAAI;oBACrB,KAAK,KAAK,GAAG,IAAI;oBACjB,qBAAqB,KAAK,mBAAmB,IAAI;gBACnD;YACF;QACF;6BAAG;QAAC;KAAK;IAET,yCAAyC;IACzC,MAAM,eAAe,CAAC,MAAc;QAClC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QACD,gDAAgD;QAChD,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,QAAQ,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YACtC,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,eAAe,CAAC,IAAI,IAAI;YACpC,UAAU,eAAe,GAAG;QAC9B;QAEA,IAAI,SAAS,KAAK,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1D,UAAU,KAAK,GAAG;QACpB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,8BAA8B;IAC9B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,4BAA4B;YAC5B,MAAM,WAAW;gBACf,GAAG,QAAQ;YACb;YAEA,mDAAmD;YACnD,IAAI,QAAQ,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;gBACrC,OAAO,AAAC,SAAiB,QAAQ;YACnC;YAEA,8CAA8C;YAC9C,IAAI,SAAS,aAAa,EAAE;gBAC1B,SAAS,aAAa,GAAG,SAAS,aAAa;YACjD;YAEA,IAAI;YACJ,IAAI,MAAM;gBACR,8BAA8B;gBAC9B,SAAS,MAAM,oHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;YACrD,OAAO;gBACL,uBAAuB;gBACvB,SAAS,MAAM,oHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;YACrC;YAEA,OAAO;QACT,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU,IAAI,OAAO,IAAI;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;8BACP,OAAO,CAAC,iBAAiB,EAAE,KAAK,QAAQ,EAAE,GAAG;;;;;;;;;;;0BAGlD,6LAAC,mIAAA,CAAA,cAAW;;oBACT,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;kCAIjC,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;gDACxD,UAAU;gDACV,WAAW,OAAO,QAAQ,GAAG,mBAAmB;;;;;;4CAEjD,OAAO,QAAQ,kBAAI,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,QAAQ;;;;;;;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DACZ,OAAO,qDAAqD;;;;;;0DAE/D,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;gDACxD,UAAU;gDACV,WAAW,OAAO,QAAQ,GAAG,mBAAmB;;;;;;4CAEjD,OAAO,QAAQ,kBAAI,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,QAAQ;;;;;;;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,eAAe;gDAC/B,UAAU,CAAC,IAAM,aAAa,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDAC/D,UAAU;gDACV,WAAW,OAAO,eAAe,GAAG,mBAAmB;;;;;;4CAExD,OAAO,eAAe,kBAAI,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,eAAe;;;;;;;;;;;;kDAIxF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK;gDACrD,UAAU;gDACV,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;4CAE9C,OAAO,KAAK,kBAAI,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,KAAK;;;;;;;;;;;;kDAIpE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,SAAS;gDACzB,UAAU,CAAC,IAAM,aAAa,aAAa,EAAE,MAAM,CAAC,KAAK;gDACzD,UAAU;;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAU;;;;;;0DACzB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;gDACvD,UAAU;;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAM;;;;;;0DACrB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,GAAG;gDACnB,UAAU,CAAC,IAAM,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;gDACnD,UAAU;;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAsB;;;;;;0DACrC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,mBAAmB;gDACnC,UAAU,CAAC,IAAM,aAAa,uBAAuB,EAAE,MAAM,CAAC,KAAK;gDACnE,UAAU;;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAC7D,UAAU;;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,KAAK;gDACrB,eAAe,CAAC,QAAU,aAAa,SAAS;gDAChD,UAAU;;kEAEV,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;0EACzB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,SAAS,SAAS,SAAS;wCAC3B,iBAAiB,CAAC,UAAY,aAAa,aAAa;wCACxD,UAAU,WAAY,QAAQ,KAAK,KAAK,KAAK;;;;;;kDAE/C,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;;;;;;;0CAI7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,kBAAe;wCACd,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,oBAAM,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;kDACpB;;;;;;kDAGD,6LAAC,iJAAA,CAAA,gBAAa;wCACZ,MAAK;wCACL,SAAS;wCACT,oBAAM,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACtB,IAAI;kDAEH,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GA/SwB;KAAA", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/DatabaseView.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { PrimaryButton } from '@/components/ui/animated-button'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\n\nimport { usersApi } from '@/lib/api'\nimport { Loader2, Database, RefreshCw, Eye } from 'lucide-react'\n\nexport default function DatabaseView() {\n  const [dbData, setDbData] = useState<any>(null)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  // Carica tutti i dati del database\n  const loadDatabaseData = async () => {\n    setLoading(true)\n    setError('')\n\n    try {\n      console.log('Caricamento dati database raw...')\n      const data = await usersApi.getDatabaseData()\n      console.log('Dati ricevuti:', data)\n      setDbData(data)\n    } catch (err: any) {\n      console.error('Errore durante il caricamento:', err)\n      setError(err.response?.data?.detail || err.message || 'Errore durante il caricamento dei dati del database')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    loadDatabaseData()\n  }, [])\n\n  // Renderizza una tabella specifica\n  const renderTable = (tableName: string, tableData: any[], title: string) => {\n    if (!tableData || tableData.length === 0) {\n      return (\n        <div className=\"text-center py-4 text-slate-500 border rounded-lg\">\n          Nessun dato disponibile per {title}\n        </div>\n      )\n    }\n\n    // Ottieni le chiavi per le colonne (dal primo elemento)\n    const columns = Object.keys(tableData[0])\n\n    return (\n      <div className=\"border rounded-lg overflow-hidden mb-6\">\n        <div className=\"bg-slate-100 px-4 py-3 border-b\">\n          <h4 className=\"font-medium text-slate-900\">{title}</h4>\n          <p className=\"text-sm text-slate-600\">Totale record: {tableData.length}</p>\n        </div>\n        <div className=\"overflow-x-auto max-h-96\">\n          <Table>\n            <TableHeader className=\"sticky top-0 bg-slate-50\">\n              <TableRow>\n                {columns.map((column) => (\n                  <TableHead key={column} className=\"font-medium\">\n                    {column}\n                  </TableHead>\n                ))}\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {tableData.map((row, index) => (\n                <TableRow key={index}>\n                  {columns.map((column) => (\n                    <TableCell key={column} className=\"font-mono text-sm\">\n                      {row[column] !== null && row[column] !== undefined\n                        ? String(row[column])\n                        : <span className=\"text-slate-400\">NULL</span>\n                      }\n                    </TableCell>\n                  ))}\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </div>\n      </div>\n    )\n  }\n\n  // Definisce le tabelle disponibili\n  const tables = [\n    { key: 'users', title: 'Utenti', description: 'Tutti gli utenti del sistema' },\n    { key: 'cantieri', title: 'Cantieri', description: 'Tutti i cantieri/progetti' },\n    { key: 'cavi', title: 'Cavi', description: 'Tutti i cavi installati' },\n    { key: 'parco_cavi', title: 'Bobine', description: 'Tutte le bobine del parco cavi' },\n    { key: 'strumenti_certificati', title: 'Strumenti', description: 'Strumenti certificati' },\n    { key: 'certificazioni_cavi', title: 'Certificazioni', description: 'Certificazioni dei cavi' }\n  ]\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <Database className=\"h-5 w-5\" />\n            Visualizzazione Database Raw\n          </CardTitle>\n          <PrimaryButton\n            size=\"sm\"\n            onClick={loadDatabaseData}\n            loading={loading}\n            icon={<RefreshCw className=\"h-4 w-4\" />}\n          >\n            Aggiorna\n          </PrimaryButton>\n        </div>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-start gap-3\">\n            <Eye className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n            <div>\n              <h4 className=\"font-medium text-blue-900\">Visualizzazione Raw del Database</h4>\n              <p className=\"text-sm text-blue-700 mt-1\">\n                Questa sezione mostra i dati grezzi delle tabelle del database. \n                Utile per debugging e analisi dei dati.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {loading ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <Loader2 className=\"h-8 w-8 animate-spin mr-3\" />\n            <span className=\"text-lg\">Caricamento dati database...</span>\n          </div>\n        ) : error ? (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-6\">\n            <p className=\"text-red-600 font-medium\">Errore durante il caricamento:</p>\n            <p className=\"text-red-600\">{error}</p>\n          </div>\n        ) : !dbData ? (\n          <div className=\"text-center py-12 text-slate-500\">\n            Nessun dato disponibile\n          </div>\n        ) : (\n          <div className=\"space-y-8\">\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <p className=\"text-sm text-blue-700\">\n                Visualizzazione completa di tutte le tabelle del database.\n                I dati sono mostrati in formato raw per debugging e analisi.\n              </p>\n            </div>\n\n            {tables.map((table) => (\n              dbData[table.key] && (\n                <div key={table.key}>\n                  <div className=\"mb-4\">\n                    <h3 className=\"text-xl font-semibold text-slate-900\">{table.title}</h3>\n                    <p className=\"text-sm text-slate-600\">{table.description}</p>\n                  </div>\n                  {renderTable(table.key, dbData[table.key], table.title)}\n                </div>\n              )\n            ))}\n\n            {/* Mostra un riepilogo */}\n            <div className=\"bg-slate-50 border border-slate-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-slate-900 mb-2\">Riepilogo Database</h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm\">\n                {tables.map((table) => (\n                  <div key={table.key} className=\"flex justify-between\">\n                    <span className=\"text-slate-600\">{table.title}:</span>\n                    <span className=\"font-medium\">\n                      {dbData[table.key] ? dbData[table.key].length : 0} record\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;;;AATA;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,mCAAmC;IACnC,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,OAAO,MAAM,oHAAA,CAAA,WAAQ,CAAC,eAAe;YAC3C,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,UAAU;QACZ,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU,IAAI,OAAO,IAAI;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,cAAc,CAAC,WAAmB,WAAkB;QACxD,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;YACxC,qBACE,6LAAC;gBAAI,WAAU;;oBAAoD;oBACpC;;;;;;;QAGnC;QAEA,wDAAwD;QACxD,MAAM,UAAU,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE;QAExC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAC5C,6LAAC;4BAAE,WAAU;;gCAAyB;gCAAgB,UAAU,MAAM;;;;;;;;;;;;;8BAExE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;0CACJ,6LAAC,oIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC,oIAAA,CAAA,WAAQ;8CACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,oIAAA,CAAA,YAAS;4CAAc,WAAU;sDAC/B;2CADa;;;;;;;;;;;;;;;0CAMtB,6LAAC,oIAAA,CAAA,YAAS;0CACP,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC,oIAAA,CAAA,WAAQ;kDACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,oIAAA,CAAA,YAAS;gDAAc,WAAU;0DAC/B,GAAG,CAAC,OAAO,KAAK,QAAQ,GAAG,CAAC,OAAO,KAAK,YACrC,OAAO,GAAG,CAAC,OAAO,kBAClB,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;+CAHvB;;;;;uCAFL;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgB7B;IAEA,mCAAmC;IACnC,MAAM,SAAS;QACb;YAAE,KAAK;YAAS,OAAO;YAAU,aAAa;QAA+B;QAC7E;YAAE,KAAK;YAAY,OAAO;YAAY,aAAa;QAA4B;QAC/E;YAAE,KAAK;YAAQ,OAAO;YAAQ,aAAa;QAA0B;QACrE;YAAE,KAAK;YAAc,OAAO;YAAU,aAAa;QAAiC;QACpF;YAAE,KAAK;YAAyB,OAAO;YAAa,aAAa;QAAwB;QACzF;YAAE,KAAK;YAAuB,OAAO;YAAkB,aAAa;QAA0B;KAC/F;IAED,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,6LAAC,iJAAA,CAAA,gBAAa;4BACZ,MAAK;4BACL,SAAS;4BACT,SAAS;4BACT,oBAAM,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;sCAC5B;;;;;;;;;;;;;;;;;0BAKL,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;oBAQ/C,wBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;+BAE1B,sBACF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;+BAE7B,CAAC,uBACH,6LAAC;wBAAI,WAAU;kCAAmC;;;;;6CAIlD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;4BAMtC,OAAO,GAAG,CAAC,CAAC,QACX,MAAM,CAAC,MAAM,GAAG,CAAC,kBACf,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwC,MAAM,KAAK;;;;;;8DACjE,6LAAC;oDAAE,WAAU;8DAA0B,MAAM,WAAW;;;;;;;;;;;;wCAEzD,YAAY,MAAM,GAAG,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,KAAK;;mCAL9C,MAAM,GAAG;;;;;0CAWvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gDAAoB,WAAU;;kEAC7B,6LAAC;wDAAK,WAAU;;4DAAkB,MAAM,KAAK;4DAAC;;;;;;;kEAC9C,6LAAC;wDAAK,WAAU;;4DACb,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG;4DAAE;;;;;;;;+CAH5C,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcrC;GA7KwB;KAAA", "debugId": null}}, {"offset": {"line": 1564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/ResetDatabase.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { DangerButton, SecondaryButton } from '@/components/ui/animated-button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { usersApi } from '@/lib/api'\nimport { Loader2, RotateCcw, AlertTriangle, Trash2 } from 'lucide-react'\n\nexport default function ResetDatabase() {\n  const [confirmText, setConfirmText] = useState('')\n  const [confirmChecked, setConfirmChecked] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState('')\n\n  const handleReset = async () => {\n    if (confirmText !== 'RESET DATABASE' || !confirmChecked) {\n      setError('Conferma richiesta per procedere con il reset')\n      return\n    }\n\n    setLoading(true)\n    setError('')\n    setSuccess('')\n\n    try {\n      await usersApi.resetDatabase()\n      setSuccess('Database resettato con successo! Tutti i dati sono stati eliminati.')\n      setConfirmText('')\n      setConfirmChecked(false)\n    } catch (err: any) {\n      setError(err.response?.data?.detail || err.message || 'Errore durante il reset del database')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const isResetEnabled = confirmText === 'RESET DATABASE' && confirmChecked && !loading\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2 text-red-600\">\n          <RotateCcw className=\"h-5 w-5\" />\n          Reset Database\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Avviso di pericolo */}\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-start gap-3\">\n            <AlertTriangle className=\"h-6 w-6 text-red-600 mt-0.5\" />\n            <div>\n              <h4 className=\"font-bold text-red-900 text-lg\">⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE</h4>\n              <div className=\"text-red-700 mt-2 space-y-2\">\n                <p className=\"font-medium\">\n                  Questa operazione eliminerà PERMANENTEMENTE tutti i dati dal database:\n                </p>\n                <ul className=\"list-disc list-inside space-y-1 text-sm\">\n                  <li>Tutti gli utenti (eccetto l'amministratore principale)</li>\n                  <li>Tutti i cantieri e i progetti</li>\n                  <li>Tutti i cavi installati</li>\n                  <li>Tutte le bobine del parco cavi</li>\n                  <li>Tutti i comandi e le certificazioni</li>\n                  <li>Tutti i report e i dati di produttività</li>\n                </ul>\n                <p className=\"font-bold text-red-800 mt-3\">\n                  NON È POSSIBILE RECUPERARE I DATI DOPO IL RESET!\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Messaggi di stato */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n            <p className=\"text-red-600\">{error}</p>\n          </div>\n        )}\n\n        {success && (\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n            <p className=\"text-green-600\">{success}</p>\n          </div>\n        )}\n\n        {/* Form di conferma */}\n        <div className=\"space-y-4 border-t pt-6\">\n          <div>\n            <h4 className=\"font-semibold text-slate-900 mb-4\">\n              Conferma Reset Database\n            </h4>\n            <p className=\"text-sm text-slate-600 mb-4\">\n              Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:\n            </p>\n          </div>\n\n          <div className=\"space-y-4\">\n            {/* Step 1: Digitare testo di conferma */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"confirm-text\" className=\"text-sm font-medium\">\n                1. Digita esattamente: <code className=\"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold\">RESET DATABASE</code>\n              </Label>\n              <Input\n                id=\"confirm-text\"\n                value={confirmText}\n                onChange={(e) => setConfirmText(e.target.value)}\n                placeholder=\"Digita: RESET DATABASE\"\n                disabled={loading}\n                className={confirmText === 'RESET DATABASE' ? 'border-green-500' : ''}\n              />\n            </div>\n\n            {/* Step 2: Checkbox di conferma */}\n            <div className=\"flex items-start space-x-3\">\n              <Checkbox\n                id=\"confirm-checkbox\"\n                checked={confirmChecked}\n                onCheckedChange={setConfirmChecked}\n                disabled={loading}\n              />\n              <Label htmlFor=\"confirm-checkbox\" className=\"text-sm leading-relaxed\">\n                2. Confermo di aver compreso che questa operazione eliminerà TUTTI i dati dal database \n                in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario.\n              </Label>\n            </div>\n          </div>\n\n          {/* Stato di conferma */}\n          <div className=\"bg-slate-50 border border-slate-200 rounded-lg p-4\">\n            <h5 className=\"font-medium text-slate-900 mb-2\">Stato Conferma:</h5>\n            <div className=\"space-y-1 text-sm\">\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-3 h-3 rounded-full ${\n                  confirmText === 'RESET DATABASE' ? 'bg-green-500' : 'bg-red-500'\n                }`}></div>\n                <span>Testo di conferma: {confirmText === 'RESET DATABASE' ? '✓ Corretto' : '✗ Richiesto'}</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-3 h-3 rounded-full ${\n                  confirmChecked ? 'bg-green-500' : 'bg-red-500'\n                }`}></div>\n                <span>Checkbox confermata: {confirmChecked ? '✓ Sì' : '✗ Richiesta'}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Pulsante di reset */}\n          <DangerButton\n            onClick={handleReset}\n            disabled={!isResetEnabled}\n            className=\"w-full\"\n            size=\"lg\"\n            loading={loading}\n            icon={<Trash2 className=\"h-5 w-5\" />}\n            glow\n          >\n            {loading ? 'Reset in corso...' : 'RESET DATABASE - ELIMINA TUTTI I DATI'}\n          </DangerButton>\n\n          {!isResetEnabled && (\n            <p className=\"text-center text-sm text-slate-500\">\n              Completa tutti i passaggi di conferma per abilitare il reset\n            </p>\n          )}\n        </div>\n\n        {/* Informazioni aggiuntive */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm\">\n          <h5 className=\"font-medium text-blue-900 mb-2\">Informazioni Tecniche:</h5>\n          <ul className=\"text-blue-700 space-y-1\">\n            <li>• Il reset manterrà la struttura delle tabelle</li>\n            <li>• L'utente amministratore principale verrà ricreato</li>\n            <li>• Le configurazioni di sistema verranno ripristinate ai valori di default</li>\n            <li>• L'operazione può richiedere alcuni minuti per completarsi</li>\n          </ul>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AAVA;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,cAAc;QAClB,IAAI,gBAAgB,oBAAoB,CAAC,gBAAgB;YACvD,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,oHAAA,CAAA,WAAQ,CAAC,aAAa;YAC5B,WAAW;YACX,eAAe;YACf,kBAAkB;QACpB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU,IAAI,OAAO,IAAI;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,gBAAgB,oBAAoB,kBAAkB,CAAC;IAE9E,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIrC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAG3B,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;;;;;;;8DAEN,6LAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASlD,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;oBAIhC,yBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;kCAKnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAGlD,6LAAC;wCAAE,WAAU;kDAA8B;;;;;;;;;;;;0CAK7C,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAe,WAAU;;oDAAsB;kEACrC,6LAAC;wDAAK,WAAU;kEAAwD;;;;;;;;;;;;0DAEjG,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,aAAY;gDACZ,UAAU;gDACV,WAAW,gBAAgB,mBAAmB,qBAAqB;;;;;;;;;;;;kDAKvE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,SAAS;gDACT,iBAAiB;gDACjB,UAAU;;;;;;0DAEZ,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAmB,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;0CAQ1E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,gBAAgB,mBAAmB,iBAAiB,cACpD;;;;;;kEACF,6LAAC;;4DAAK;4DAAoB,gBAAgB,mBAAmB,eAAe;;;;;;;;;;;;;0DAE9E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,iBAAiB,iBAAiB,cAClC;;;;;;kEACF,6LAAC;;4DAAK;4DAAsB,iBAAiB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;0CAM5D,6LAAC,iJAAA,CAAA,eAAY;gCACX,SAAS;gCACT,UAAU,CAAC;gCACX,WAAU;gCACV,MAAK;gCACL,SAAS;gCACT,oBAAM,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCACxB,IAAI;0CAEH,UAAU,sBAAsB;;;;;;4BAGlC,CAAC,gCACA,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;kCAOtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB;GA7KwB;KAAA", "debugId": null}}, {"offset": {"line": 2085, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/TipologieCaviManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Cable, Building, FileText, Tag, Plus } from 'lucide-react'\n\nexport default function TipologieCaviManager() {\n  const [activeTab, setActiveTab] = useState('categorie')\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Cable className=\"h-5 w-5\" />\n          Database Tipologie Cavi\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-start gap-3\">\n            <Cable className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n            <div>\n              <h4 className=\"font-medium text-blue-900\">Database Enciclopedico Tipologie Cavi</h4>\n              <p className=\"text-sm text-blue-700 mt-1\">\n                Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, \n                standard e tipologie specifiche. Questo database serve come riferimento per \n                la classificazione e gestione dei cavi nei progetti.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-4\">\n            <TabsTrigger value=\"categorie\" className=\"flex items-center gap-2\">\n              <Tag className=\"h-4 w-4\" />\n              Categorie\n            </TabsTrigger>\n            <TabsTrigger value=\"produttori\" className=\"flex items-center gap-2\">\n              <Building className=\"h-4 w-4\" />\n              Produttori\n            </TabsTrigger>\n            <TabsTrigger value=\"standard\" className=\"flex items-center gap-2\">\n              <FileText className=\"h-4 w-4\" />\n              Standard\n            </TabsTrigger>\n            <TabsTrigger value=\"tipologie\" className=\"flex items-center gap-2\">\n              <Cable className=\"h-4 w-4\" />\n              Tipologie\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"categorie\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Categorie Cavi</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuova Categoria\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <Tag className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione categorie cavi - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile creare, modificare ed eliminare le categorie di cavi\n              </p>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"produttori\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Produttori</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuovo Produttore\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <Building className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione produttori - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile gestire l'anagrafica dei produttori di cavi\n              </p>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"standard\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Standard e Normative</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuovo Standard\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <FileText className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione standard - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile gestire gli standard tecnici e le normative di riferimento\n              </p>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"tipologie\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Tipologie Specifiche</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuova Tipologia\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <Cable className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione tipologie - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate\n              </p>\n            </div>\n          </TabsContent>\n        </Tabs>\n\n        {/* Informazioni aggiuntive */}\n        <div className=\"bg-slate-50 border border-slate-200 rounded-lg p-4\">\n          <h5 className=\"font-medium text-slate-900 mb-2\">Struttura Database Tipologie:</h5>\n          <div className=\"text-sm text-slate-600 space-y-1\">\n            <p><strong>Categorie:</strong> Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)</p>\n            <p><strong>Produttori:</strong> Aziende produttrici con informazioni di contatto</p>\n            <p><strong>Standard:</strong> Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)</p>\n            <p><strong>Tipologie:</strong> Specifiche tecniche dettagliate per ogni tipo di cavo</p>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIjC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;kCAShD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,OAAO;wBAAW,eAAe;wBAAc,WAAU;;0CAC7D,6LAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG7B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAa,WAAU;;0DACxC,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;;0DACtC,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAKjC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;;kDACvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,6LAAC,qIAAA,CAAA,SAAM;;kEACL,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;0DAAE;;;;;;0DACH,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAMhC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAa,WAAU;;kDACxC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,6LAAC,qIAAA,CAAA,SAAM;;kEACL,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAE;;;;;;0DACH,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAMhC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDACtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,6LAAC,qIAAA,CAAA,SAAM;;kEACL,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAE;;;;;;0DACH,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAMhC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;;kDACvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,6LAAC,qIAAA,CAAA,SAAM;;kEACL,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAE;;;;;;0DACH,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;;;;;;;kCAQlC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAmB;;;;;;;kDAC9B,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAoB;;;;;;;kDAC/B,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAkB;;;;;;;kDAC7B,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1C;GAxJwB;KAAA", "debugId": null}}, {"offset": {"line": 2752, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { AnimatedButton, PrimaryButton, SecondaryButton, DangerButton, OutlineButton, QuickButton } from '@/components/ui/animated-button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { api, cantieriApi, usersApi } from '@/lib/api'\nimport { User, Cantiere } from '@/types'\nimport UserForm from '@/components/admin/UserForm'\nimport DatabaseView from '@/components/admin/DatabaseView'\nimport ResetDatabase from '@/components/admin/ResetDatabase'\nimport TipologieCaviManager from '@/components/admin/TipologieCaviManager'\nimport {\n  Settings,\n  Users,\n  Building2,\n  Search,\n  Plus,\n  Edit,\n  Trash2,\n  CheckCircle,\n  Clock,\n  AlertCircle,\n  Eye,\n  Shield,\n  Key,\n  Loader2,\n  UserPlus,\n  LogIn,\n  Cable,\n  Database,\n  RotateCcw,\n  RefreshCw\n} from 'lucide-react'\n\nexport default function AdminPage() {\n  const [activeTab, setActiveTab] = useState('visualizza-utenti')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [users, setUsers] = useState<User[]>([])\n  const [cantieri, setCantieri] = useState<Cantiere[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [selectedUser, setSelectedUser] = useState<User | null>(null)\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' })\n\n  const { user } = useAuth()\n\n  // Carica dati dal backend\n  useEffect(() => {\n    loadData()\n  }, [activeTab])\n\n  const loadData = async () => {\n    try {\n      setIsLoading(true)\n      setError('')\n\n      console.log('Caricamento dati per tab:', activeTab)\n      console.log('Token presente:', typeof window !== 'undefined' ? localStorage.getItem('token') : 'N/A')\n      console.log('Utente corrente:', user)\n\n      if (activeTab === 'visualizza-utenti' || activeTab === 'crea-utente' || activeTab === 'accedi-come-utente') {\n        console.log('Chiamata API per ottenere utenti...')\n        const usersData = await usersApi.getUsers()\n        console.log('Utenti ricevuti:', usersData)\n        setUsers(usersData)\n      } else if (activeTab === 'cantieri') {\n        const cantieriData = await cantieriApi.getCantieri()\n        setCantieri(cantieriData)\n      }\n    } catch (error: any) {\n      console.error('Errore caricamento dati:', error)\n      console.error('Dettagli errore:', error.response)\n      setError(error.response?.data?.detail || error.message || 'Errore durante il caricamento dei dati')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleEditUser = (userToEdit: User) => {\n    setSelectedUser(userToEdit)\n    setActiveTab('modifica-utente')\n  }\n\n  const handleToggleUserStatus = async (userId: number) => {\n    try {\n      await usersApi.toggleUserStatus(userId)\n      loadData() // Ricarica i dati\n    } catch (error: any) {\n      console.error('Errore toggle status:', error)\n      setError(error.response?.data?.detail || 'Errore durante la modifica dello stato utente')\n    }\n  }\n\n  const handleDeleteUser = async (userId: number) => {\n    if (confirm('Sei sicuro di voler eliminare questo utente?')) {\n      try {\n        await usersApi.deleteUser(userId)\n        loadData() // Ricarica i dati\n      } catch (error: any) {\n        console.error('Errore eliminazione utente:', error)\n        setError(error.response?.data?.detail || 'Errore durante l\\'eliminazione dell\\'utente')\n      }\n    }\n  }\n\n  const handleSaveUser = (savedUser: User) => {\n    console.log('Utente salvato:', savedUser)\n    setSelectedUser(null)\n    setActiveTab('visualizza-utenti')\n    loadData() // Ricarica i dati\n  }\n\n  const handleCancelForm = () => {\n    setSelectedUser(null)\n    setActiveTab('visualizza-utenti')\n  }\n\n  // Helper functions per i badge\n\n  const getRuoloBadge = (ruolo: string) => {\n    switch (ruolo) {\n      case 'owner':\n        return <Badge className=\"bg-purple-100 text-purple-800\">Owner</Badge>\n      case 'user':\n        return <Badge className=\"bg-blue-100 text-blue-800\">User</Badge>\n      case 'cantieri_user':\n        return <Badge className=\"bg-green-100 text-green-800\">Cantieri User</Badge>\n      default:\n        return <Badge variant=\"secondary\">{ruolo}</Badge>\n    }\n  }\n\n  const getStatusBadge = (abilitato: boolean, data_scadenza?: string) => {\n    if (!abilitato) {\n      return <Badge className=\"bg-red-100 text-red-800\">Disabilitato</Badge>\n    }\n    \n    if (data_scadenza) {\n      const scadenza = new Date(data_scadenza)\n      const oggi = new Date()\n      \n      if (scadenza < oggi) {\n        return <Badge className=\"bg-red-100 text-red-800\">Scaduto</Badge>\n      } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {\n        return <Badge className=\"bg-yellow-100 text-yellow-800\">In Scadenza</Badge>\n      }\n    }\n    \n    return <Badge className=\"bg-green-100 text-green-800\">Attivo</Badge>\n  }\n\n  const filteredUsers = users.filter(u =>\n    u.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    u.ragione_sociale?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    u.email?.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  // Verifica se l'utente ha permessi di amministrazione\n  if (user?.ruolo !== 'owner') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-6\">\n        <Card className=\"w-full max-w-md\">\n          <CardContent className=\"p-6 text-center\">\n            <Shield className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n            <h2 className=\"text-xl font-bold text-slate-900 mb-2\">Accesso Negato</h2>\n            <p className=\"text-slate-600\">Non hai i permessi necessari per accedere a questa sezione.</p>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n\n\n\n        {/* Tabs - Stile sottile senza hover invasivi */}\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className={`grid w-full ${selectedUser ? 'grid-cols-6' : 'grid-cols-5'}`}>\n            <TabsTrigger value=\"visualizza-utenti\" className=\"tab-trigger flex items-center gap-2\">\n              <Users className=\"h-4 w-4\" />\n              Visualizza Utenti\n            </TabsTrigger>\n            <TabsTrigger value=\"crea-utente\" className=\"tab-trigger flex items-center gap-2\">\n              <UserPlus className=\"h-4 w-4\" />\n              Crea Nuovo Utente\n            </TabsTrigger>\n            {selectedUser && (\n              <TabsTrigger value=\"modifica-utente\" className=\"tab-trigger flex items-center gap-2\">\n                <Edit className=\"h-4 w-4\" />\n                Modifica Utente\n              </TabsTrigger>\n            )}\n            <TabsTrigger value=\"database-tipologie-cavi\" className=\"tab-trigger flex items-center gap-2\">\n              <Cable className=\"h-4 w-4\" />\n              Database Tipologie Cavi\n            </TabsTrigger>\n            <TabsTrigger value=\"visualizza-database-raw\" className=\"tab-trigger flex items-center gap-2\">\n              <Database className=\"h-4 w-4\" />\n              Visualizza Database Raw\n            </TabsTrigger>\n            <TabsTrigger value=\"reset-database\" className=\"tab-trigger flex items-center gap-2\">\n              <RotateCcw className=\"h-4 w-4\" />\n              Reset Database\n            </TabsTrigger>\n          </TabsList>\n\n          {/* Tab Visualizza Utenti */}\n          <TabsContent value=\"visualizza-utenti\" className=\"space-y-4\">\n\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                <p className=\"text-red-600\">{error}</p>\n              </div>\n            )}\n\n            <Card>\n              <CardHeader>\n                <CardTitle>Lista Utenti</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"rounded-md border\">\n                  <Table>\n                    <TableHeader>\n                      <TableRow>\n                        <TableHead>ID</TableHead>\n                        <TableHead>Username</TableHead>\n                        <TableHead>Password</TableHead>\n                        <TableHead>Ruolo</TableHead>\n                        <TableHead>Ragione Sociale</TableHead>\n                        <TableHead>Email</TableHead>\n                        <TableHead>VAT</TableHead>\n                        <TableHead>Nazione</TableHead>\n                        <TableHead>Referente</TableHead>\n                        <TableHead>Scadenza</TableHead>\n                        <TableHead>Stato</TableHead>\n                        <TableHead>Azioni</TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {isLoading ? (\n                        <TableRow>\n                          <TableCell colSpan={12} className=\"text-center py-8\">\n                            <div className=\"flex items-center justify-center gap-2\">\n                              <Loader2 className=\"h-4 w-4 animate-spin\" />\n                              Caricamento...\n                            </div>\n                          </TableCell>\n                        </TableRow>\n                      ) : users.length === 0 ? (\n                        <TableRow>\n                          <TableCell colSpan={12} className=\"text-center py-8 text-slate-500\">\n                            Nessun utente trovato\n                          </TableCell>\n                        </TableRow>\n                      ) : (\n                        users.map((utente) => (\n                          <TableRow key={utente.id_utente}>\n                            <TableCell>{utente.id_utente}</TableCell>\n                            <TableCell className=\"font-medium\">{utente.username}</TableCell>\n                            <TableCell>{utente.password_plain || '***'}</TableCell>\n                            <TableCell>{getRuoloBadge(utente.ruolo)}</TableCell>\n                            <TableCell>{utente.ragione_sociale || '-'}</TableCell>\n                            <TableCell>{utente.email || '-'}</TableCell>\n                            <TableCell>{utente.vat || '-'}</TableCell>\n                            <TableCell>{utente.nazione || '-'}</TableCell>\n                            <TableCell>{utente.referente_aziendale || '-'}</TableCell>\n                            <TableCell>\n                              {utente.data_scadenza ?\n                                new Date(utente.data_scadenza).toLocaleDateString('it-IT') :\n                                'N/A'\n                              }\n                            </TableCell>\n                            <TableCell>{getStatusBadge(utente.abilitato, utente.data_scadenza)}</TableCell>\n                            <TableCell>\n                              <div className=\"flex gap-1\">\n                                <QuickButton\n                                  size=\"sm\"\n                                  onClick={() => handleEditUser(utente)}\n                                  title=\"Modifica\"\n                                  className=\"p-2\"\n                                >\n                                  <Edit className=\"h-4 w-4\" />\n                                </QuickButton>\n                                <QuickButton\n                                  size=\"sm\"\n                                  onClick={() => handleToggleUserStatus(utente.id_utente)}\n                                  title={utente.abilitato ? 'Disabilita' : 'Abilita'}\n                                  disabled={utente.ruolo === 'owner'}\n                                  className=\"p-2\"\n                                >\n                                  {utente.abilitato ? <Clock className=\"h-4 w-4 text-red-600\" /> : <CheckCircle className=\"h-4 w-4 text-green-600\" />}\n                                </QuickButton>\n                                <QuickButton\n                                  size=\"sm\"\n                                  onClick={() => handleDeleteUser(utente.id_utente)}\n                                  title=\"Elimina\"\n                                  disabled={utente.ruolo === 'owner'}\n                                  className=\"p-2\"\n                                >\n                                  <Trash2 className=\"h-4 w-4 text-red-600\" />\n                                </QuickButton>\n                              </div>\n                            </TableCell>\n                          </TableRow>\n                        ))\n                      )}\n                    </TableBody>\n                  </Table>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Tab Crea Nuovo Utente */}\n          <TabsContent value=\"crea-utente\" className=\"space-y-4\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-slate-900\">Crea Nuovo Utente Standard</h2>\n              <p className=\"text-slate-600\">Da qui puoi creare un nuovo utente standard nel sistema.</p>\n            </div>\n            <UserForm\n              user={null}\n              onSave={handleSaveUser}\n              onCancel={handleCancelForm}\n            />\n          </TabsContent>\n\n          {/* Tab Modifica Utente - Visibile solo quando un utente è selezionato */}\n          {selectedUser && (\n            <TabsContent value=\"modifica-utente\" className=\"space-y-4\">\n              <div>\n                <h2 className=\"text-2xl font-bold text-slate-900\">Modifica Utente: {selectedUser.username}</h2>\n                <p className=\"text-slate-600\">Da qui puoi modificare i dati dell'utente selezionato.</p>\n              </div>\n              <UserForm\n                user={selectedUser}\n                onSave={handleSaveUser}\n                onCancel={handleCancelForm}\n              />\n            </TabsContent>\n          )}\n\n\n\n          {/* Tab Database Tipologie Cavi */}\n          <TabsContent value=\"database-tipologie-cavi\" className=\"space-y-4\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-slate-900\">Database Tipologie Cavi</h2>\n              <p className=\"text-slate-600\">Gestisci il database enciclopedico delle tipologie di cavi: categorie, produttori, standard e tipologie.</p>\n            </div>\n            <TipologieCaviManager />\n          </TabsContent>\n\n          {/* Tab Visualizza Database Raw */}\n          <TabsContent value=\"visualizza-database-raw\" className=\"space-y-4\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-slate-900\">Visualizzazione Database Raw</h2>\n              <p className=\"text-slate-600\">Questa sezione mostra una visualizzazione raw del database. Puoi vedere i dati delle tabelle principali.</p>\n            </div>\n            <DatabaseView />\n          </TabsContent>\n\n          {/* Tab Reset Database */}\n          <TabsContent value=\"reset-database\" className=\"space-y-4\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-slate-900\">Reset Database</h2>\n              <p className=\"text-slate-600\">Attenzione: questa operazione cancellerà tutti i dati del database.</p>\n            </div>\n            <ResetDatabase />\n          </TabsContent>\n\n        </Tabs>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAjBA;;;;;;;;;;;;;;AAwCe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAO,SAAS;QAAI,UAAU;IAAU;IAEjG,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;QAAC;KAAU;IAEd,MAAM,WAAW;QACf,IAAI;YACF,aAAa;YACb,SAAS;YAET,QAAQ,GAAG,CAAC,6BAA6B;YACzC,QAAQ,GAAG,CAAC,mBAAmB,uCAAgC,aAAa,OAAO,CAAC;YACpF,QAAQ,GAAG,CAAC,oBAAoB;YAEhC,IAAI,cAAc,uBAAuB,cAAc,iBAAiB,cAAc,sBAAsB;gBAC1G,QAAQ,GAAG,CAAC;gBACZ,MAAM,YAAY,MAAM,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACzC,QAAQ,GAAG,CAAC,oBAAoB;gBAChC,SAAS;YACX,OAAO,IAAI,cAAc,YAAY;gBACnC,MAAM,eAAe,MAAM,oHAAA,CAAA,cAAW,CAAC,WAAW;gBAClD,YAAY;YACd;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ;YAChD,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;QAC5D,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,oHAAA,CAAA,WAAQ,CAAC,gBAAgB,CAAC;YAChC,WAAW,kBAAkB;;QAC/B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,QAAQ,iDAAiD;YAC3D,IAAI;gBACF,MAAM,oHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;gBAC1B,WAAW,kBAAkB;;YAC/B,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;YAC3C;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,gBAAgB;QAChB,aAAa;QACb,WAAW,kBAAkB;;IAC/B;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,aAAa;IACf;IAEA,+BAA+B;IAE/B,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,MAAM,iBAAiB,CAAC,WAAoB;QAC1C,IAAI,CAAC,WAAW;YACd,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA0B;;;;;;QACpD;QAEA,IAAI,eAAe;YACjB,MAAM,WAAW,IAAI,KAAK;YAC1B,MAAM,OAAO,IAAI;YAEjB,IAAI,WAAW,MAAM;gBACnB,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA0B;;;;;;YACpD,OAAO,IAAI,SAAS,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM;gBACxE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D;QACF;QAEA,qBAAO,6LAAC,oIAAA,CAAA,QAAK;YAAC,WAAU;sBAA8B;;;;;;IACxD;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,IACjC,EAAE,QAAQ,EAAE,cAAc,SAAS,WAAW,WAAW,OACzD,EAAE,eAAe,EAAE,cAAc,SAAS,WAAW,WAAW,OAChE,EAAE,KAAK,EAAE,cAAc,SAAS,WAAW,WAAW;IAGxD,sDAAsD;IACtD,IAAI,MAAM,UAAU,SAAS;QAC3B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAiB;;;;;;;;;;;;;;;;;;;;;;IAKxC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBAKb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;gBAAc,WAAU;;kCAC7D,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAW,CAAC,YAAY,EAAE,eAAe,gBAAgB,eAAe;;0CAChF,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAoB,WAAU;;kDAC/C,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAc,WAAU;;kDACzC,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;4BAGjC,8BACC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAkB,WAAU;;kDAC7C,6LAAC,8MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAIhC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAA0B,WAAU;;kDACrD,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAA0B,WAAU;;kDACrD,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAiB,WAAU;;kDAC5C,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAMrC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAoB,WAAU;;4BAE9C,uBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;0CAIjC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kEACJ,6LAAC,oIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8EACP,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;;;;;;;;;;;;kEAGf,6LAAC,oIAAA,CAAA,YAAS;kEACP,0BACC,6LAAC,oIAAA,CAAA,WAAQ;sEACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gEAAC,SAAS;gEAAI,WAAU;0EAChC,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAAyB;;;;;;;;;;;;;;;;mEAKhD,MAAM,MAAM,KAAK,kBACnB,6LAAC,oIAAA,CAAA,WAAQ;sEACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gEAAC,SAAS;gEAAI,WAAU;0EAAkC;;;;;;;;;;mEAKtE,MAAM,GAAG,CAAC,CAAC,uBACT,6LAAC,oIAAA,CAAA,WAAQ;;kFACP,6LAAC,oIAAA,CAAA,YAAS;kFAAE,OAAO,SAAS;;;;;;kFAC5B,6LAAC,oIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAe,OAAO,QAAQ;;;;;;kFACnD,6LAAC,oIAAA,CAAA,YAAS;kFAAE,OAAO,cAAc,IAAI;;;;;;kFACrC,6LAAC,oIAAA,CAAA,YAAS;kFAAE,cAAc,OAAO,KAAK;;;;;;kFACtC,6LAAC,oIAAA,CAAA,YAAS;kFAAE,OAAO,eAAe,IAAI;;;;;;kFACtC,6LAAC,oIAAA,CAAA,YAAS;kFAAE,OAAO,KAAK,IAAI;;;;;;kFAC5B,6LAAC,oIAAA,CAAA,YAAS;kFAAE,OAAO,GAAG,IAAI;;;;;;kFAC1B,6LAAC,oIAAA,CAAA,YAAS;kFAAE,OAAO,OAAO,IAAI;;;;;;kFAC9B,6LAAC,oIAAA,CAAA,YAAS;kFAAE,OAAO,mBAAmB,IAAI;;;;;;kFAC1C,6LAAC,oIAAA,CAAA,YAAS;kFACP,OAAO,aAAa,GACnB,IAAI,KAAK,OAAO,aAAa,EAAE,kBAAkB,CAAC,WAClD;;;;;;kFAGJ,6LAAC,oIAAA,CAAA,YAAS;kFAAE,eAAe,OAAO,SAAS,EAAE,OAAO,aAAa;;;;;;kFACjE,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,iJAAA,CAAA,cAAW;oFACV,MAAK;oFACL,SAAS,IAAM,eAAe;oFAC9B,OAAM;oFACN,WAAU;8FAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;;;;;;8FAElB,6LAAC,iJAAA,CAAA,cAAW;oFACV,MAAK;oFACL,SAAS,IAAM,uBAAuB,OAAO,SAAS;oFACtD,OAAO,OAAO,SAAS,GAAG,eAAe;oFACzC,UAAU,OAAO,KAAK,KAAK;oFAC3B,WAAU;8FAET,OAAO,SAAS,iBAAG,6LAAC,uMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;6GAA4B,6LAAC,8NAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;8FAE1F,6LAAC,iJAAA,CAAA,cAAW;oFACV,MAAK;oFACL,SAAS,IAAM,iBAAiB,OAAO,SAAS;oFAChD,OAAM;oFACN,UAAU,OAAO,KAAK,KAAK;oFAC3B,WAAU;8FAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+DA3CX,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA0D/C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAc,WAAU;;0CACzC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAE,WAAU;kDAAiB;;;;;;;;;;;;0CAEhC,6LAAC,0IAAA,CAAA,UAAQ;gCACP,MAAM;gCACN,QAAQ;gCACR,UAAU;;;;;;;;;;;;oBAKb,8BACC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAkB,WAAU;;0CAC7C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;4CAAoC;4CAAkB,aAAa,QAAQ;;;;;;;kDACzF,6LAAC;wCAAE,WAAU;kDAAiB;;;;;;;;;;;;0CAEhC,6LAAC,0IAAA,CAAA,UAAQ;gCACP,MAAM;gCACN,QAAQ;gCACR,UAAU;;;;;;;;;;;;kCAQhB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAA0B,WAAU;;0CACrD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAE,WAAU;kDAAiB;;;;;;;;;;;;0CAEhC,6LAAC,sJAAA,CAAA,UAAoB;;;;;;;;;;;kCAIvB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAA0B,WAAU;;0CACrD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAE,WAAU;kDAAiB;;;;;;;;;;;;0CAEhC,6LAAC,8IAAA,CAAA,UAAY;;;;;;;;;;;kCAIf,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAiB,WAAU;;0CAC5C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAE,WAAU;kDAAiB;;;;;;;;;;;;0CAEhC,6LAAC,+IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;GAvVwB;;QAUL,kIAAA,CAAA,UAAO;;;KAVF", "debugId": null}}]}