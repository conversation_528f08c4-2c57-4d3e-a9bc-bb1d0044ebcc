"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_animated_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/animated-button */ \"(app-pages-browser)/./src/components/ui/animated-button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_admin_UserForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/UserForm */ \"(app-pages-browser)/./src/components/admin/UserForm.tsx\");\n/* harmony import */ var _components_admin_ImpersonateUser__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/ImpersonateUser */ \"(app-pages-browser)/./src/components/admin/ImpersonateUser.tsx\");\n/* harmony import */ var _components_admin_DatabaseView__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/admin/DatabaseView */ \"(app-pages-browser)/./src/components/admin/DatabaseView.tsx\");\n/* harmony import */ var _components_admin_ResetDatabase__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/admin/ResetDatabase */ \"(app-pages-browser)/./src/components/admin/ResetDatabase.tsx\");\n/* harmony import */ var _components_admin_TipologieCaviManager__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/TipologieCaviManager */ \"(app-pages-browser)/./src/components/admin/TipologieCaviManager.tsx\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cable.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('visualizza-utenti');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cantieri, setCantieri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [notification, setNotification] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        message: '',\n        severity: 'success'\n    });\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    // Carica dati dal backend\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            loadData();\n        }\n    }[\"AdminPage.useEffect\"], [\n        activeTab\n    ]);\n    const loadData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError('');\n            console.log('Caricamento dati per tab:', activeTab);\n            console.log('Token presente:',  true ? localStorage.getItem('token') : 0);\n            console.log('Utente corrente:', user);\n            if (activeTab === 'visualizza-utenti' || activeTab === 'crea-utente' || activeTab === 'accedi-come-utente') {\n                console.log('Chiamata API per ottenere utenti...');\n                const usersData = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.usersApi.getUsers();\n                console.log('Utenti ricevuti:', usersData);\n                setUsers(usersData);\n            } else if (activeTab === 'cantieri') {\n                const cantieriData = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.cantieriApi.getCantieri();\n                setCantieri(cantieriData);\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Errore caricamento dati:', error);\n            console.error('Dettagli errore:', error.response);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || error.message || 'Errore durante il caricamento dei dati');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEditUser = (userToEdit)=>{\n        setSelectedUser(userToEdit);\n        setActiveTab('modifica-utente');\n    };\n    const handleToggleUserStatus = async (userId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_9__.usersApi.toggleUserStatus(userId);\n            loadData() // Ricarica i dati\n            ;\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Errore toggle status:', error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || 'Errore durante la modifica dello stato utente');\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (confirm('Sei sicuro di voler eliminare questo utente?')) {\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_9__.usersApi.deleteUser(userId);\n                loadData() // Ricarica i dati\n                ;\n            } catch (error) {\n                var _error_response_data, _error_response;\n                console.error('Errore eliminazione utente:', error);\n                setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || 'Errore durante l\\'eliminazione dell\\'utente');\n            }\n        }\n    };\n    const handleSaveUser = (savedUser)=>{\n        console.log('Utente salvato:', savedUser);\n        setSelectedUser(null);\n        setActiveTab('visualizza-utenti');\n        loadData() // Ricarica i dati\n        ;\n    };\n    const handleCancelForm = ()=>{\n        setSelectedUser(null);\n        setActiveTab('visualizza-utenti');\n    };\n    // Helper functions per i badge\n    const getRuoloBadge = (ruolo)=>{\n        switch(ruolo){\n            case 'owner':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-purple-100 text-purple-800\",\n                    children: \"Owner\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, this);\n            case 'user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, this);\n            case 'cantieri_user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"Cantieri User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: ruolo\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (abilitato, data_scadenza)=>{\n        if (!abilitato) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                className: \"bg-red-100 text-red-800\",\n                children: \"Disabilitato\"\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 14\n            }, this);\n        }\n        if (data_scadenza) {\n            const scadenza = new Date(data_scadenza);\n            const oggi = new Date();\n            if (scadenza < oggi) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-red-100 text-red-800\",\n                    children: \"Scaduto\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 16\n                }, this);\n            } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"In Scadenza\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 16\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            className: \"bg-green-100 text-green-800\",\n            children: \"Attivo\"\n        }, void 0, false, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 12\n        }, this);\n    };\n    const filteredUsers = users.filter((u)=>{\n        var _u_username, _u_ragione_sociale, _u_email;\n        return ((_u_username = u.username) === null || _u_username === void 0 ? void 0 : _u_username.toLowerCase().includes(searchTerm.toLowerCase())) || ((_u_ragione_sociale = u.ragione_sociale) === null || _u_ragione_sociale === void 0 ? void 0 : _u_ragione_sociale.toLowerCase().includes(searchTerm.toLowerCase())) || ((_u_email = u.email) === null || _u_email === void 0 ? void 0 : _u_email.toLowerCase().includes(searchTerm.toLowerCase()));\n    });\n    // Verifica se l'utente ha permessi di amministrazione\n    if ((user === null || user === void 0 ? void 0 : user.ruolo) !== 'owner') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-slate-900 mb-2\",\n                            children: \"Accesso Negato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-slate-600\",\n                            children: \"Non hai i permessi necessari per accedere a questa sezione.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-slate-900 flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Pannello Admin\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 mt-1\",\n                                    children: \"Questa sezione mostra la lista di tutti gli utenti del sistema.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        activeTab === 'crea-utente' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_4__.PrimaryButton, {\n                            size: \"sm\",\n                            onClick: ()=>setActiveTab('visualizza-utenti'),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 21\n                            }, void 0),\n                            glow: true,\n                            children: \"Aggiorna\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                            className: \"grid w-full \".concat(selectedUser ? 'grid-cols-7' : 'grid-cols-6'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"visualizza-utenti\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Visualizza Utenti\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"crea-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Crea Nuovo Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"modifica-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Modifica Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"accedi-come-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Accedi come Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"database-tipologie-cavi\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Database Tipologie Cavi\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"visualizza-database-raw\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Visualizza Database Raw\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"reset-database\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Reset Database\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"visualizza-utenti\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-slate-900\",\n                                                    children: \"Visualizza Utenti\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600\",\n                                                    children: \"Questa sezione mostra la lista di tutti gli utenti del sistema.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_4__.PrimaryButton, {\n                                            onClick: loadData,\n                                            loading: isLoading,\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            size: \"sm\",\n                                            children: \"Aggiorna\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Lista Utenti\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-md border\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Username\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Ruolo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Ragione Sociale\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"VAT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Nazione\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Referente\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Scadenza\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Stato\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Azioni\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                    colSpan: 12,\n                                                                    className: \"text-center py-8\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"h-4 w-4 animate-spin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 292,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Caricamento...\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 25\n                                                            }, this) : users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                    colSpan: 12,\n                                                                    className: \"text-center py-8 text-slate-500\",\n                                                                    children: \"Nessun utente trovato\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 25\n                                                            }, this) : users.map((utente)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.id_utente\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            className: \"font-medium\",\n                                                                            children: utente.username\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.password_plain || '***'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 308,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: getRuoloBadge(utente.ruolo)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.ragione_sociale || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.email || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.vat || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 312,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.nazione || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.referente_aziendale || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.data_scadenza ? new Date(utente.data_scadenza).toLocaleDateString('it-IT') : 'N/A'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: getStatusBadge(utente.abilitato, utente.data_scadenza)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleEditUser(utente),\n                                                                                        title: \"Modifica\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 330,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 324,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleToggleUserStatus(utente.id_utente),\n                                                                                        title: utente.abilitato ? 'Disabilita' : 'Abilita',\n                                                                                        disabled: utente.ruolo === 'owner',\n                                                                                        children: utente.abilitato ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 339,\n                                                                                            columnNumber: 55\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-red-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 339,\n                                                                                            columnNumber: 108\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 332,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleDeleteUser(utente.id_utente),\n                                                                                        title: \"Elimina\",\n                                                                                        disabled: utente.ruolo === 'owner',\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-red-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 348,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 341,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 323,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, utente.id_utente, true, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"crea-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Crea Nuovo Utente Standard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi creare un nuovo utente standard nel sistema.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UserForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    user: null,\n                                    onSave: handleSaveUser,\n                                    onCancel: handleCancelForm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this),\n                        selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"modifica-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: [\n                                                \"Modifica Utente: \",\n                                                selectedUser.username\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi modificare i dati dell'utente selezionato.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UserForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    user: selectedUser,\n                                    onSave: handleSaveUser,\n                                    onCancel: handleCancelForm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"accedi-come-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Accedi come Utente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi accedere al sistema impersonando un altro utente.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ImpersonateUser__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"database-tipologie-cavi\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Database Tipologie Cavi\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Gestisci il database enciclopedico delle tipologie di cavi: categorie, produttori, standard e tipologie.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_TipologieCaviManager__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"visualizza-database-raw\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Visualizzazione Database Raw\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Questa sezione mostra una visualizzazione raw del database. Puoi vedere i dati delle tabelle principali.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_DatabaseView__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"reset-database\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Reset Database\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Attenzione: questa operazione canceller\\xe0 tutti i dati del database.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ResetDatabase__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"WAwpFRDbUhtFhmQPH9w/cXXy7RA=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});