"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/ImpersonateUser.tsx":
/*!**************************************************!*\
  !*** ./src/components/admin/ImpersonateUser.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImpersonateUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_animated_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/animated-button */ \"(app-pages-browser)/./src/components/ui/animated-button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,LogIn!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,LogIn!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,LogIn!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ImpersonateUser() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { impersonateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedUserId, setSelectedUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingUsers, setLoadingUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Carica gli utenti all'avvio del componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImpersonateUser.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"ImpersonateUser.useEffect\"], []);\n    const loadUsers = async ()=>{\n        setLoadingUsers(true);\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.usersApi.getUsers();\n            // Filtra solo gli utenti non amministratori\n            const nonAdminUsers = data.filter((user)=>user.ruolo !== 'owner');\n            setUsers(nonAdminUsers);\n            setError('');\n        } catch (err) {\n            var _err_response_data, _err_response;\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || err.message || 'Errore durante il caricamento degli utenti');\n        } finally{\n            setLoadingUsers(false);\n        }\n    };\n    // Gestisce l'accesso come utente selezionato\n    const handleImpersonate = async ()=>{\n        if (!selectedUserId) {\n            setError('Seleziona un utente');\n            return;\n        }\n        setLoading(true);\n        try {\n            // Trova l'utente selezionato per ottenere il ruolo\n            const selectedUser = users.find((user)=>user.id_utente === parseInt(selectedUserId));\n            if (!selectedUser) {\n                throw new Error('Utente non trovato');\n            }\n            // Utilizza la funzione impersonateUser dal contesto di autenticazione\n            const userData = await impersonateUser(parseInt(selectedUserId));\n            console.log('Impersonificazione utente:', selectedUser.username, 'Ruolo:', selectedUser.ruolo);\n            // Reindirizza in base al ruolo dell'utente impersonato\n            // L'amministratore mantiene i suoi privilegi ma accede alle funzionalità dell'utente impersonato\n            if (selectedUser.ruolo === 'user') {\n                // Utente standard - vai alla pagina dei cantieri\n                router.push('/cantieri');\n            } else if (selectedUser.ruolo === 'cantieri_user') {\n                // Utente cantiere - vai alla pagina di visualizzazione cavi\n                router.push('/cavi');\n            } else {\n                // Fallback - vai alla homepage\n                router.push('/');\n            }\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error('Errore durante l\\'impersonificazione:', err);\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || err.message || 'Errore durante l\\'accesso come utente selezionato');\n            setLoading(false);\n        }\n    };\n    const getRuoloBadge = (ruolo)=>{\n        switch(ruolo){\n            case 'user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                    children: \"User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 16\n                }, this);\n            case 'cantieri_user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                    children: \"Cantieri User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                    children: ruolo\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        \"Accedi come Utente\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-600 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-blue-900\",\n                                            children: \"Informazioni sull'impersonificazione\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 mt-1\",\n                                            children: \"Questa funzionalit\\xe0 ti permette di accedere al sistema come se fossi un altro utente, mantenendo i tuoi privilegi di amministratore. Utile per testare le funzionalit\\xe0 o assistere gli utenti.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"user-select\",\n                                        children: \"Seleziona Utente\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this),\n                                    loadingUsers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-6 w-6 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: \"Caricamento utenti...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this) : users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-slate-500\",\n                                        children: \"Nessun utente disponibile per l'impersonificazione\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                        value: selectedUserId,\n                                        onValueChange: setSelectedUserId,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                    placeholder: \"Seleziona un utente da impersonare\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                        value: user.id_utente.toString(),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: user.username\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                                            lineNumber: 145,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-slate-500\",\n                                                                            children: user.ragione_sociale || 'N/A'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                                            lineNumber: 146,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: getRuoloBadge(user.ruolo)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, user.id_utente, false, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            selectedUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-50 border border-slate-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-slate-900 mb-2\",\n                                        children: \"Utente Selezionato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    (()=>{\n                                        const selectedUser = users.find((user)=>user.id_utente === parseInt(selectedUserId));\n                                        if (!selectedUser) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Username:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: selectedUser.username\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Ragione Sociale:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: selectedUser.ragione_sociale || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Ruolo:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: getRuoloBadge(selectedUser.ruolo)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Email:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: selectedUser.email || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Stato:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(selectedUser.abilitato ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                            children: selectedUser.abilitato ? 'Attivo' : 'Disabilitato'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 19\n                                        }, this);\n                                    })()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_4__.PrimaryButton, {\n                                onClick: handleImpersonate,\n                                disabled: !selectedUserId || loading || loadingUsers,\n                                className: \"w-full\",\n                                loading: loading,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 19\n                                }, void 0),\n                                glow: true,\n                                children: loading ? 'Accesso in corso...' : 'Accedi come Utente Selezionato'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(ImpersonateUser, \"gMH5a4Hucol3+Ok/MT2XwxSRpIk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = ImpersonateUser;\nvar _c;\n$RefreshReg$(_c, \"ImpersonateUser\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/ImpersonateUser.tsx\n"));

/***/ })

});