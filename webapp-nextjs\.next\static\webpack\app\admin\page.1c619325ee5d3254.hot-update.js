"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/ResetDatabase.tsx":
/*!************************************************!*\
  !*** ./src/components/admin/ResetDatabase.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResetDatabase)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/animated-button */ \"(app-pages-browser)/./src/components/ui/animated-button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ResetDatabase() {\n    _s();\n    const [confirmText, setConfirmText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmChecked, setConfirmChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleReset = async ()=>{\n        if (confirmText !== 'RESET DATABASE' || !confirmChecked) {\n            setError('Conferma richiesta per procedere con il reset');\n            return;\n        }\n        setLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_7__.usersApi.resetDatabase();\n            setSuccess('Database resettato con successo! Tutti i dati sono stati eliminati.');\n            setConfirmText('');\n            setConfirmChecked(false);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || err.message || 'Errore durante il reset del database');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const isResetEnabled = confirmText === 'RESET DATABASE' && confirmChecked && !loading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    className: \"flex items-center gap-2 text-red-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        \"Reset Database\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-600 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-red-900 text-lg\",\n                                            children: \"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-red-700 mt-2 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Questa operazione eliminer\\xe0 PERMANENTEMENTE tutti i dati dal database:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-disc list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutti gli utenti (eccetto l'amministratore principale)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 64,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutti i cantieri e i progetti\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 65,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutti i cavi installati\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutte le bobine del parco cavi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutti i comandi e le certificazioni\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutti i report e i dati di produttivit\\xe0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-bold text-red-800 mt-3\",\n                                                    children: \"NON \\xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this),\n                    success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-600\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 border-t pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-slate-900 mb-4\",\n                                        children: \"Conferma Reset Database\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-600 mb-4\",\n                                        children: \"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"confirm-text\",\n                                                className: \"text-sm font-medium\",\n                                                children: [\n                                                    \"1. Digita esattamente: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold\",\n                                                        children: \"RESET DATABASE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 40\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"confirm-text\",\n                                                value: confirmText,\n                                                onChange: (e)=>setConfirmText(e.target.value),\n                                                placeholder: \"Digita: RESET DATABASE\",\n                                                disabled: loading,\n                                                className: confirmText === 'RESET DATABASE' ? 'border-green-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                id: \"confirm-checkbox\",\n                                                checked: confirmChecked,\n                                                onCheckedChange: setConfirmChecked,\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"confirm-checkbox\",\n                                                className: \"text-sm leading-relaxed\",\n                                                children: \"2. Confermo di aver compreso che questa operazione eliminer\\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-50 border border-slate-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"font-medium text-slate-900 mb-2\",\n                                        children: \"Stato Conferma:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full \".concat(confirmText === 'RESET DATABASE' ? 'bg-green-500' : 'bg-red-500')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Testo di conferma: \",\n                                                            confirmText === 'RESET DATABASE' ? '✓ Corretto' : '✗ Richiesto'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full \".concat(confirmChecked ? 'bg-green-500' : 'bg-red-500')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Checkbox confermata: \",\n                                                            confirmChecked ? '✓ Sì' : '✗ Richiesta'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__.DangerButton, {\n                                onClick: handleReset,\n                                disabled: !isResetEnabled,\n                                className: \"w-full\",\n                                size: \"lg\",\n                                loading: loading,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 19\n                                }, void 0),\n                                glow: true,\n                                children: loading ? 'Reset in corso...' : 'RESET DATABASE - ELIMINA TUTTI I DATI'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            !isResetEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-sm text-slate-500\",\n                                children: \"Completa tutti i passaggi di conferma per abilitare il reset\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: \"font-medium text-blue-900 mb-2\",\n                                children: \"Informazioni Tecniche:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-blue-700 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Il reset manterr\\xe0 la struttura delle tabelle\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• L'utente amministratore principale verr\\xe0 ricreato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Le configurazioni di sistema verranno ripristinate ai valori di default\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• L'operazione pu\\xf2 richiedere alcuni minuti per completarsi\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(ResetDatabase, \"UeHtS2ZDBt7D8K2doAYKuR9de3c=\");\n_c = ResetDatabase;\nvar _c;\n$RefreshReg$(_c, \"ResetDatabase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/ResetDatabase.tsx\n"));

/***/ })

});