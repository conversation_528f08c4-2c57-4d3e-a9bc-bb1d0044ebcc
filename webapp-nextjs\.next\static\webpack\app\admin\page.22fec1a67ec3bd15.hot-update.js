"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_animated_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/animated-button */ \"(app-pages-browser)/./src/components/ui/animated-button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_admin_UserForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/UserForm */ \"(app-pages-browser)/./src/components/admin/UserForm.tsx\");\n/* harmony import */ var _components_admin_ImpersonateUser__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/ImpersonateUser */ \"(app-pages-browser)/./src/components/admin/ImpersonateUser.tsx\");\n/* harmony import */ var _components_admin_DatabaseView__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/admin/DatabaseView */ \"(app-pages-browser)/./src/components/admin/DatabaseView.tsx\");\n/* harmony import */ var _components_admin_ResetDatabase__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/admin/ResetDatabase */ \"(app-pages-browser)/./src/components/admin/ResetDatabase.tsx\");\n/* harmony import */ var _components_admin_TipologieCaviManager__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/TipologieCaviManager */ \"(app-pages-browser)/./src/components/admin/TipologieCaviManager.tsx\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cable.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('visualizza-utenti');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cantieri, setCantieri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [notification, setNotification] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        message: '',\n        severity: 'success'\n    });\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    // Carica dati dal backend\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            loadData();\n        }\n    }[\"AdminPage.useEffect\"], [\n        activeTab\n    ]);\n    const loadData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError('');\n            console.log('Caricamento dati per tab:', activeTab);\n            console.log('Token presente:',  true ? localStorage.getItem('token') : 0);\n            console.log('Utente corrente:', user);\n            if (activeTab === 'visualizza-utenti' || activeTab === 'crea-utente' || activeTab === 'accedi-come-utente') {\n                console.log('Chiamata API per ottenere utenti...');\n                const usersData = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.usersApi.getUsers();\n                console.log('Utenti ricevuti:', usersData);\n                setUsers(usersData);\n            } else if (activeTab === 'cantieri') {\n                const cantieriData = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.cantieriApi.getCantieri();\n                setCantieri(cantieriData);\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Errore caricamento dati:', error);\n            console.error('Dettagli errore:', error.response);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || error.message || 'Errore durante il caricamento dei dati');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEditUser = (userToEdit)=>{\n        setSelectedUser(userToEdit);\n        setActiveTab('modifica-utente');\n    };\n    const handleToggleUserStatus = async (userId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_9__.usersApi.toggleUserStatus(userId);\n            loadData() // Ricarica i dati\n            ;\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Errore toggle status:', error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || 'Errore durante la modifica dello stato utente');\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (confirm('Sei sicuro di voler eliminare questo utente?')) {\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_9__.usersApi.deleteUser(userId);\n                loadData() // Ricarica i dati\n                ;\n            } catch (error) {\n                var _error_response_data, _error_response;\n                console.error('Errore eliminazione utente:', error);\n                setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || 'Errore durante l\\'eliminazione dell\\'utente');\n            }\n        }\n    };\n    const handleSaveUser = (savedUser)=>{\n        console.log('Utente salvato:', savedUser);\n        setSelectedUser(null);\n        setActiveTab('visualizza-utenti');\n        loadData() // Ricarica i dati\n        ;\n    };\n    const handleCancelForm = ()=>{\n        setSelectedUser(null);\n        setActiveTab('visualizza-utenti');\n    };\n    // Helper functions per i badge\n    const getRuoloBadge = (ruolo)=>{\n        switch(ruolo){\n            case 'owner':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-purple-100 text-purple-800\",\n                    children: \"Owner\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, this);\n            case 'user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, this);\n            case 'cantieri_user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"Cantieri User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: ruolo\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (abilitato, data_scadenza)=>{\n        if (!abilitato) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                className: \"bg-red-100 text-red-800\",\n                children: \"Disabilitato\"\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 14\n            }, this);\n        }\n        if (data_scadenza) {\n            const scadenza = new Date(data_scadenza);\n            const oggi = new Date();\n            if (scadenza < oggi) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-red-100 text-red-800\",\n                    children: \"Scaduto\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 16\n                }, this);\n            } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"In Scadenza\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 16\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            className: \"bg-green-100 text-green-800\",\n            children: \"Attivo\"\n        }, void 0, false, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 12\n        }, this);\n    };\n    const filteredUsers = users.filter((u)=>{\n        var _u_username, _u_ragione_sociale, _u_email;\n        return ((_u_username = u.username) === null || _u_username === void 0 ? void 0 : _u_username.toLowerCase().includes(searchTerm.toLowerCase())) || ((_u_ragione_sociale = u.ragione_sociale) === null || _u_ragione_sociale === void 0 ? void 0 : _u_ragione_sociale.toLowerCase().includes(searchTerm.toLowerCase())) || ((_u_email = u.email) === null || _u_email === void 0 ? void 0 : _u_email.toLowerCase().includes(searchTerm.toLowerCase()));\n    });\n    // Verifica se l'utente ha permessi di amministrazione\n    if ((user === null || user === void 0 ? void 0 : user.ruolo) !== 'owner') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-slate-900 mb-2\",\n                            children: \"Accesso Negato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-slate-600\",\n                            children: \"Non hai i permessi necessari per accedere a questa sezione.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-slate-900 flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Pannello Admin\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 mt-1\",\n                                    children: \"Questa sezione mostra la lista di tutti gli utenti del sistema.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        activeTab === 'crea-utente' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_4__.PrimaryButton, {\n                            size: \"sm\",\n                            onClick: ()=>setActiveTab('visualizza-utenti'),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 21\n                            }, void 0),\n                            glow: true,\n                            children: \"Aggiorna\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                            className: \"grid w-full \".concat(selectedUser ? 'grid-cols-7' : 'grid-cols-6'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"visualizza-utenti\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Visualizza Utenti\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"crea-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Crea Nuovo Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"modifica-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Modifica Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"accedi-come-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Accedi come Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"database-tipologie-cavi\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Database Tipologie Cavi\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"visualizza-database-raw\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Visualizza Database Raw\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"reset-database\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Reset Database\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"visualizza-utenti\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-slate-900\",\n                                                    children: \"Visualizza Utenti\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600\",\n                                                    children: \"Questa sezione mostra la lista di tutti gli utenti del sistema.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: loadData,\n                                            disabled: isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Aggiorna\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Lista Utenti\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-md border\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Username\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Ruolo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Ragione Sociale\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"VAT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Nazione\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Referente\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Scadenza\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Stato\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                        children: \"Azioni\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                    colSpan: 12,\n                                                                    className: \"text-center py-8\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"h-4 w-4 animate-spin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 288,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Caricamento...\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 25\n                                                            }, this) : users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                    colSpan: 12,\n                                                                    className: \"text-center py-8 text-slate-500\",\n                                                                    children: \"Nessun utente trovato\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 25\n                                                            }, this) : users.map((utente)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.id_utente\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 302,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            className: \"font-medium\",\n                                                                            children: utente.username\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.password_plain || '***'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: getRuoloBadge(utente.ruolo)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.ragione_sociale || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.email || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.vat || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 308,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.nazione || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.referente_aziendale || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: utente.data_scadenza ? new Date(utente.data_scadenza).toLocaleDateString('it-IT') : 'N/A'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: getStatusBadge(utente.abilitato, utente.data_scadenza)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleEditUser(utente),\n                                                                                        title: \"Modifica\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 326,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 320,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleToggleUserStatus(utente.id_utente),\n                                                                                        title: utente.abilitato ? 'Disabilita' : 'Abilita',\n                                                                                        disabled: utente.ruolo === 'owner',\n                                                                                        children: utente.abilitato ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 335,\n                                                                                            columnNumber: 55\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-red-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 335,\n                                                                                            columnNumber: 108\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 328,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleDeleteUser(utente.id_utente),\n                                                                                        title: \"Elimina\",\n                                                                                        disabled: utente.ruolo === 'owner',\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-red-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 344,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 337,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 319,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 318,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, utente.id_utente, true, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"crea-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Crea Nuovo Utente Standard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi creare un nuovo utente standard nel sistema.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UserForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    user: null,\n                                    onSave: handleSaveUser,\n                                    onCancel: handleCancelForm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this),\n                        selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"modifica-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: [\n                                                \"Modifica Utente: \",\n                                                selectedUser.username\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi modificare i dati dell'utente selezionato.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UserForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    user: selectedUser,\n                                    onSave: handleSaveUser,\n                                    onCancel: handleCancelForm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"accedi-come-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Accedi come Utente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi accedere al sistema impersonando un altro utente.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ImpersonateUser__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"database-tipologie-cavi\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Database Tipologie Cavi\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Gestisci il database enciclopedico delle tipologie di cavi: categorie, produttori, standard e tipologie.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_TipologieCaviManager__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"visualizza-database-raw\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Visualizzazione Database Raw\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Questa sezione mostra una visualizzazione raw del database. Puoi vedere i dati delle tabelle principali.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_DatabaseView__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"reset-database\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Reset Database\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Attenzione: questa operazione canceller\\xe0 tutti i dati del database.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ResetDatabase__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"WAwpFRDbUhtFhmQPH9w/cXXy7RA=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/animated-button.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/animated-button.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedButton: () => (/* binding */ AnimatedButton),\n/* harmony export */   DangerButton: () => (/* binding */ DangerButton),\n/* harmony export */   OutlineButton: () => (/* binding */ OutlineButton),\n/* harmony export */   PrimaryButton: () => (/* binding */ PrimaryButton),\n/* harmony export */   SecondaryButton: () => (/* binding */ SecondaryButton),\n/* harmony export */   SuccessButton: () => (/* binding */ SuccessButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n\n\n\n\nconst AnimatedButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef((param, ref)=>{\n    let { className, variant = 'primary', size = 'md', loading = false, glow = false, icon, children, disabled, ...props } = param;\n    const baseClasses = 'relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none';\n    const variantClasses = {\n        primary: 'btn-primary',\n        secondary: 'btn-secondary',\n        success: 'btn-success',\n        danger: 'btn-danger',\n        outline: 'btn-outline'\n    };\n    const sizeClasses = {\n        sm: 'btn-sm',\n        md: 'px-6 py-3',\n        lg: 'btn-lg'\n    };\n    const isDisabled = disabled || loading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variantClasses[variant], sizeClasses[size], glow && 'btn-glow', isDisabled && 'opacity-50 cursor-not-allowed hover:scale-100 hover:shadow-none', className),\n        disabled: isDisabled,\n        ref: ref,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\animated-button.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"relative flex items-center justify-center gap-2\",\n                children: [\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin btn-icon\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\animated-button.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, undefined) : icon ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"btn-icon\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\animated-button.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, undefined) : null,\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\animated-button.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\animated-button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\n_c = AnimatedButton;\nAnimatedButton.displayName = 'AnimatedButton';\n// Componenti specifici per facilità d'uso\nconst PrimaryButton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedButton, {\n        variant: \"primary\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\animated-button.tsx\",\n        lineNumber: 79,\n        columnNumber: 3\n    }, undefined);\n_c1 = PrimaryButton;\nconst SecondaryButton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedButton, {\n        variant: \"secondary\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\animated-button.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined);\n_c2 = SecondaryButton;\nconst SuccessButton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedButton, {\n        variant: \"success\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\animated-button.tsx\",\n        lineNumber: 87,\n        columnNumber: 3\n    }, undefined);\n_c3 = SuccessButton;\nconst DangerButton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedButton, {\n        variant: \"danger\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\animated-button.tsx\",\n        lineNumber: 91,\n        columnNumber: 3\n    }, undefined);\n_c4 = DangerButton;\nconst OutlineButton = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedButton, {\n        variant: \"outline\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\ui\\\\animated-button.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined);\n_c5 = OutlineButton;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AnimatedButton\");\n$RefreshReg$(_c1, \"PrimaryButton\");\n$RefreshReg$(_c2, \"SecondaryButton\");\n$RefreshReg$(_c3, \"SuccessButton\");\n$RefreshReg$(_c4, \"DangerButton\");\n$RefreshReg$(_c5, \"OutlineButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/animated-button.tsx\n"));

/***/ })

});