"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/ImpersonateUser.tsx":
/*!**************************************************!*\
  !*** ./src/components/admin/ImpersonateUser.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImpersonateUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,LogIn!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,LogIn!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,LogIn!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ImpersonateUser() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { impersonateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedUserId, setSelectedUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingUsers, setLoadingUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Carica gli utenti all'avvio del componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImpersonateUser.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"ImpersonateUser.useEffect\"], []);\n    const loadUsers = async ()=>{\n        setLoadingUsers(true);\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.usersApi.getUsers();\n            // Filtra solo gli utenti non amministratori\n            const nonAdminUsers = data.filter((user)=>user.ruolo !== 'owner');\n            setUsers(nonAdminUsers);\n            setError('');\n        } catch (err) {\n            var _err_response_data, _err_response;\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || err.message || 'Errore durante il caricamento degli utenti');\n        } finally{\n            setLoadingUsers(false);\n        }\n    };\n    // Gestisce l'accesso come utente selezionato\n    const handleImpersonate = async ()=>{\n        if (!selectedUserId) {\n            setError('Seleziona un utente');\n            return;\n        }\n        setLoading(true);\n        try {\n            // Trova l'utente selezionato per ottenere il ruolo\n            const selectedUser = users.find((user)=>user.id_utente === parseInt(selectedUserId));\n            if (!selectedUser) {\n                throw new Error('Utente non trovato');\n            }\n            // Utilizza la funzione impersonateUser dal contesto di autenticazione\n            const userData = await impersonateUser(parseInt(selectedUserId));\n            console.log('Impersonificazione utente:', selectedUser.username, 'Ruolo:', selectedUser.ruolo);\n            // Reindirizza in base al ruolo dell'utente impersonato\n            // L'amministratore mantiene i suoi privilegi ma accede alle funzionalità dell'utente impersonato\n            if (selectedUser.ruolo === 'user') {\n                // Utente standard - vai alla pagina dei cantieri\n                router.push('/cantieri');\n            } else if (selectedUser.ruolo === 'cantieri_user') {\n                // Utente cantiere - vai alla pagina di visualizzazione cavi\n                router.push('/cavi');\n            } else {\n                // Fallback - vai alla homepage\n                router.push('/');\n            }\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error('Errore durante l\\'impersonificazione:', err);\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || err.message || 'Errore durante l\\'accesso come utente selezionato');\n            setLoading(false);\n        }\n    };\n    const getRuoloBadge = (ruolo)=>{\n        switch(ruolo){\n            case 'user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                    children: \"User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 16\n                }, this);\n            case 'cantieri_user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                    children: \"Cantieri User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                    children: ruolo\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        \"Accedi come Utente\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-600 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-blue-900\",\n                                            children: \"Informazioni sull'impersonificazione\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 mt-1\",\n                                            children: \"Questa funzionalit\\xe0 ti permette di accedere al sistema come se fossi un altro utente, mantenendo i tuoi privilegi di amministratore. Utile per testare le funzionalit\\xe0 o assistere gli utenti.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"user-select\",\n                                        children: \"Seleziona Utente\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this),\n                                    loadingUsers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-6 w-6 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: \"Caricamento utenti...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this) : users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-slate-500\",\n                                        children: \"Nessun utente disponibile per l'impersonificazione\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                        value: selectedUserId,\n                                        onValueChange: setSelectedUserId,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                    placeholder: \"Seleziona un utente da impersonare\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                        value: user.id_utente.toString(),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: user.username\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                                            lineNumber: 145,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-slate-500\",\n                                                                            children: user.ragione_sociale || 'N/A'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                                            lineNumber: 146,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: getRuoloBadge(user.ruolo)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, user.id_utente, false, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            selectedUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-50 border border-slate-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-slate-900 mb-2\",\n                                        children: \"Utente Selezionato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    (()=>{\n                                        const selectedUser = users.find((user)=>user.id_utente === parseInt(selectedUserId));\n                                        if (!selectedUser) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Username:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: selectedUser.username\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Ragione Sociale:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: selectedUser.ragione_sociale || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Ruolo:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: getRuoloBadge(selectedUser.ruolo)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Email:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: selectedUser.email || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Stato:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(selectedUser.abilitato ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                            children: selectedUser.abilitato ? 'Attivo' : 'Disabilitato'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 19\n                                        }, this);\n                                    })()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleImpersonate,\n                                disabled: !selectedUserId || loading || loadingUsers,\n                                className: \"w-full\",\n                                children: [\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    loading ? 'Accesso in corso...' : 'Accedi come Utente Selezionato'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ImpersonateUser.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(ImpersonateUser, \"gMH5a4Hucol3+Ok/MT2XwxSRpIk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = ImpersonateUser;\nvar _c;\n$RefreshReg$(_c, \"ImpersonateUser\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/ImpersonateUser.tsx\n"));

/***/ })

});