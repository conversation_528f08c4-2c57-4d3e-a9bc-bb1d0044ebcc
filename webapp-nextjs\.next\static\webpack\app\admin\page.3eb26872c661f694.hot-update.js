"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/DatabaseView.tsx":
/*!***********************************************!*\
  !*** ./src/components/admin/DatabaseView.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/animated-button */ \"(app-pages-browser)/./src/components/ui/animated-button.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,Loader2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,Loader2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,Loader2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,Loader2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DatabaseView() {\n    _s();\n    const [dbData, setDbData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Carica tutti i dati del database\n    const loadDatabaseData = async ()=>{\n        setLoading(true);\n        setError('');\n        try {\n            console.log('Caricamento dati database raw...');\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.usersApi.getDatabaseData();\n            console.log('Dati ricevuti:', data);\n            setDbData(data);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error('Errore durante il caricamento:', err);\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || err.message || 'Errore durante il caricamento dei dati del database');\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DatabaseView.useEffect\": ()=>{\n            loadDatabaseData();\n        }\n    }[\"DatabaseView.useEffect\"], []);\n    // Renderizza una tabella specifica\n    const renderTable = (tableName, tableData, title)=>{\n        if (!tableData || tableData.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4 text-slate-500 border rounded-lg\",\n                children: [\n                    \"Nessun dato disponibile per \",\n                    title\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this);\n        }\n        // Ottieni le chiavi per le colonne (dal primo elemento)\n        const columns = Object.keys(tableData[0]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border rounded-lg overflow-hidden mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-slate-100 px-4 py-3 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-medium text-slate-900\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-slate-600\",\n                            children: [\n                                \"Totale record: \",\n                                tableData.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"overflow-x-auto max-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.Table, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHeader, {\n                                className: \"sticky top-0 bg-slate-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                    children: columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                            className: \"font-medium\",\n                                            children: column\n                                        }, column, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableBody, {\n                                children: tableData.map((row, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                        children: columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                className: \"font-mono text-sm\",\n                                                children: row[column] !== null && row[column] !== undefined ? String(row[column]) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-slate-400\",\n                                                    children: \"NULL\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, column, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, index, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    };\n    // Definisce le tabelle disponibili\n    const tables = [\n        {\n            key: 'users',\n            title: 'Utenti',\n            description: 'Tutti gli utenti del sistema'\n        },\n        {\n            key: 'cantieri',\n            title: 'Cantieri',\n            description: 'Tutti i cantieri/progetti'\n        },\n        {\n            key: 'cavi',\n            title: 'Cavi',\n            description: 'Tutti i cavi installati'\n        },\n        {\n            key: 'parco_cavi',\n            title: 'Bobine',\n            description: 'Tutte le bobine del parco cavi'\n        },\n        {\n            key: 'strumenti_certificati',\n            title: 'Strumenti',\n            description: 'Strumenti certificati'\n        },\n        {\n            key: 'certificazioni_cavi',\n            title: 'Certificazioni',\n            description: 'Certificazioni dei cavi'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                \"Visualizzazione Database Raw\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__.PrimaryButton, {\n                            size: \"sm\",\n                            onClick: loadDatabaseData,\n                            loading: loading,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 19\n                            }, void 0),\n                            children: \"Aggiorna\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-600 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-blue-900\",\n                                            children: \"Visualizzazione Raw del Database\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 mt-1\",\n                                            children: \"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin mr-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg\",\n                                children: \"Caricamento dati database...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 font-medium\",\n                                children: \"Errore durante il caricamento:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this) : !dbData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12 text-slate-500\",\n                        children: \"Nessun dato disponibile\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: \"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            tables.map((table)=>dbData[table.key] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-slate-900\",\n                                                    children: table.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-slate-600\",\n                                                    children: table.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this),\n                                        renderTable(table.key, dbData[table.key], table.title)\n                                    ]\n                                }, table.key, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-50 border border-slate-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-slate-900 mb-2\",\n                                        children: \"Riepilogo Database\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm\",\n                                        children: tables.map((table)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-slate-600\",\n                                                        children: [\n                                                            table.title,\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            dbData[table.key] ? dbData[table.key].length : 0,\n                                                            \" record\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, table.key, true, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseView, \"BQ6ucMaaqAH6Lm7ABEXOwIYtnxE=\");\n_c = DatabaseView;\nvar _c;\n$RefreshReg$(_c, \"DatabaseView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/DatabaseView.tsx\n"));

/***/ })

});