"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/UserForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/admin/UserForm.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/animated-button */ \"(app-pages-browser)/./src/components/ui/animated-button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction UserForm(param) {\n    let { user, onSave, onCancel } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: '',\n        ruolo: 'user',\n        data_scadenza: '',\n        abilitato: true,\n        // Nuovi campi aziendali\n        ragione_sociale: '',\n        indirizzo: '',\n        nazione: '',\n        email: '',\n        vat: '',\n        referente_aziendale: ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Inizializza il form con i dati dell'utente se presente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserForm.useEffect\": ()=>{\n            if (user) {\n                setFormData({\n                    username: user.username || '',\n                    password: '',\n                    ruolo: user.ruolo || 'user',\n                    data_scadenza: user.data_scadenza ? user.data_scadenza.split('T')[0] : '',\n                    abilitato: user.abilitato !== undefined ? user.abilitato : true,\n                    // Nuovi campi aziendali\n                    ragione_sociale: user.ragione_sociale || '',\n                    indirizzo: user.indirizzo || '',\n                    nazione: user.nazione || '',\n                    email: user.email || '',\n                    vat: user.vat || '',\n                    referente_aziendale: user.referente_aziendale || ''\n                });\n            }\n        }\n    }[\"UserForm.useEffect\"], [\n        user\n    ]);\n    // Gestisce il cambio dei valori del form\n    const handleChange = (name, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Rimuovi l'errore per questo campo se presente\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: ''\n                }));\n        }\n    };\n    // Validazione del form\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.username.trim()) {\n            newErrors.username = 'Username è obbligatorio';\n        }\n        if (!user && !formData.password.trim()) {\n            newErrors.password = 'Password è obbligatoria per nuovi utenti';\n        }\n        if (!formData.ragione_sociale.trim()) {\n            newErrors.ragione_sociale = 'Ragione sociale è obbligatoria';\n        }\n        if (formData.email && !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = 'Email non valida';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    // Gestisce il submit del form\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setLoading(true);\n        setError('');\n        try {\n            // Prepara i dati da inviare\n            const userData = {\n                ...formData\n            };\n            // Rimuovi la password se è vuota (modifica utente)\n            if (user && !userData.password.trim()) {\n                delete userData.password;\n            }\n            // Converti la data in formato ISO se presente\n            if (userData.data_scadenza) {\n                userData.data_scadenza = userData.data_scadenza;\n            }\n            let result;\n            if (user) {\n                // Aggiorna l'utente esistente\n                result = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.usersApi.updateUser(user.id_utente, userData);\n            } else {\n                // Crea un nuovo utente\n                result = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.usersApi.createUser(userData);\n            }\n            onSave(result);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || err.message || 'Errore durante il salvataggio dell\\'utente');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    children: user ? \"Modifica Utente: \".concat(user.username) : 'Crea Nuovo Utente Standard'\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"username\",\n                                                children: \"Username *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"username\",\n                                                value: formData.username,\n                                                onChange: (e)=>handleChange('username', e.target.value),\n                                                disabled: loading,\n                                                className: errors.username ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"password\",\n                                                children: user ? 'Nuova Password (lascia vuoto per non modificare)' : 'Password *'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                value: formData.password,\n                                                onChange: (e)=>handleChange('password', e.target.value),\n                                                disabled: loading,\n                                                className: errors.password ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.password\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"ragione_sociale\",\n                                                children: \"Ragione Sociale *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"ragione_sociale\",\n                                                value: formData.ragione_sociale,\n                                                onChange: (e)=>handleChange('ragione_sociale', e.target.value),\n                                                disabled: loading,\n                                                className: errors.ragione_sociale ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.ragione_sociale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.ragione_sociale\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 42\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                value: formData.email,\n                                                onChange: (e)=>handleChange('email', e.target.value),\n                                                disabled: loading,\n                                                className: errors.email ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 32\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"indirizzo\",\n                                                children: \"Indirizzo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"indirizzo\",\n                                                value: formData.indirizzo,\n                                                onChange: (e)=>handleChange('indirizzo', e.target.value),\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"nazione\",\n                                                children: \"Nazione\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"nazione\",\n                                                value: formData.nazione,\n                                                onChange: (e)=>handleChange('nazione', e.target.value),\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"vat\",\n                                                children: \"VAT\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"vat\",\n                                                value: formData.vat,\n                                                onChange: (e)=>handleChange('vat', e.target.value),\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"referente_aziendale\",\n                                                children: \"Referente Aziendale\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"referente_aziendale\",\n                                                value: formData.referente_aziendale,\n                                                onChange: (e)=>handleChange('referente_aziendale', e.target.value),\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"data_scadenza\",\n                                                children: \"Data Scadenza\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"data_scadenza\",\n                                                type: \"date\",\n                                                value: formData.data_scadenza,\n                                                onChange: (e)=>handleChange('data_scadenza', e.target.value),\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"ruolo\",\n                                                children: \"Ruolo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: formData.ruolo,\n                                                onValueChange: (value)=>handleChange('ruolo', value),\n                                                disabled: loading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"user\",\n                                                                children: \"User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"cantieri_user\",\n                                                                children: \"Cantieri User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__.Checkbox, {\n                                        id: \"abilitato\",\n                                        checked: formData.abilitato,\n                                        onCheckedChange: (checked)=>handleChange('abilitato', checked),\n                                        disabled: loading || user && user.ruolo === 'owner'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"abilitato\",\n                                        children: \"Utente abilitato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__.SecondaryButton, {\n                                        type: \"button\",\n                                        onClick: onCancel,\n                                        disabled: loading,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        children: \"Annulla\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__.PrimaryButton, {\n                                        type: \"submit\",\n                                        loading: loading,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        glow: true,\n                                        children: loading ? 'Salvataggio...' : 'Salva'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(UserForm, \"TW7M8rKbFh68/iM5OJSB18q/V3U=\");\n_c = UserForm;\nvar _c;\n$RefreshReg$(_c, \"UserForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/UserForm.tsx\n"));

/***/ })

});