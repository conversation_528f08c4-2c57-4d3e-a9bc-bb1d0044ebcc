"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/DatabaseView.tsx":
/*!***********************************************!*\
  !*** ./src/components/admin/DatabaseView.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,Loader2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,Loader2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,Loader2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,Loader2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DatabaseView() {\n    _s();\n    const [dbData, setDbData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Carica tutti i dati del database\n    const loadDatabaseData = async ()=>{\n        setLoading(true);\n        setError('');\n        try {\n            console.log('Caricamento dati database raw...');\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.usersApi.getDatabaseData();\n            console.log('Dati ricevuti:', data);\n            setDbData(data);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error('Errore durante il caricamento:', err);\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || err.message || 'Errore durante il caricamento dei dati del database');\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DatabaseView.useEffect\": ()=>{\n            loadDatabaseData();\n        }\n    }[\"DatabaseView.useEffect\"], []);\n    // Renderizza una tabella specifica\n    const renderTable = (tableName, tableData, title)=>{\n        if (!tableData || tableData.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4 text-slate-500 border rounded-lg\",\n                children: [\n                    \"Nessun dato disponibile per \",\n                    title\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this);\n        }\n        // Ottieni le chiavi per le colonne (dal primo elemento)\n        const columns = Object.keys(tableData[0]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border rounded-lg overflow-hidden mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-slate-100 px-4 py-3 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-medium text-slate-900\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-slate-600\",\n                            children: [\n                                \"Totale record: \",\n                                tableData.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"overflow-x-auto max-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.Table, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHeader, {\n                                className: \"sticky top-0 bg-slate-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                    children: columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                            className: \"font-medium\",\n                                            children: column\n                                        }, column, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableBody, {\n                                children: tableData.map((row, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                        children: columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                className: \"font-mono text-sm\",\n                                                children: row[column] !== null && row[column] !== undefined ? String(row[column]) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-slate-400\",\n                                                    children: \"NULL\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, column, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, index, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    };\n    // Definisce le tabelle disponibili\n    const tables = [\n        {\n            key: 'users',\n            title: 'Utenti',\n            description: 'Tutti gli utenti del sistema'\n        },\n        {\n            key: 'cantieri',\n            title: 'Cantieri',\n            description: 'Tutti i cantieri/progetti'\n        },\n        {\n            key: 'cavi',\n            title: 'Cavi',\n            description: 'Tutti i cavi installati'\n        },\n        {\n            key: 'parco_cavi',\n            title: 'Bobine',\n            description: 'Tutte le bobine del parco cavi'\n        },\n        {\n            key: 'strumenti_certificati',\n            title: 'Strumenti',\n            description: 'Strumenti certificati'\n        },\n        {\n            key: 'certificazioni_cavi',\n            title: 'Certificazioni',\n            description: 'Certificazioni dei cavi'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                \"Visualizzazione Database Raw\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: loadDatabaseData,\n                            disabled: loading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                \"Aggiorna\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-600 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-blue-900\",\n                                            children: \"Visualizzazione Raw del Database\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 mt-1\",\n                                            children: \"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_Loader2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin mr-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg\",\n                                children: \"Caricamento dati database...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 font-medium\",\n                                children: \"Errore durante il caricamento:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this) : !dbData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12 text-slate-500\",\n                        children: \"Nessun dato disponibile\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: \"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this),\n                            tables.map((table)=>dbData[table.key] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-slate-900\",\n                                                    children: table.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-slate-600\",\n                                                    children: table.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 19\n                                        }, this),\n                                        renderTable(table.key, dbData[table.key], table.title)\n                                    ]\n                                }, table.key, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-50 border border-slate-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-slate-900 mb-2\",\n                                        children: \"Riepilogo Database\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm\",\n                                        children: tables.map((table)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-slate-600\",\n                                                        children: [\n                                                            table.title,\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            dbData[table.key] ? dbData[table.key].length : 0,\n                                                            \" record\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, table.key, true, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\DatabaseView.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseView, \"BQ6ucMaaqAH6Lm7ABEXOwIYtnxE=\");\n_c = DatabaseView;\nvar _c;\n$RefreshReg$(_c, \"DatabaseView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/DatabaseView.tsx\n"));

/***/ })

});