"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/UserForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/admin/UserForm.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction UserForm(param) {\n    let { user, onSave, onCancel } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: '',\n        ruolo: 'user',\n        data_scadenza: '',\n        abilitato: true,\n        // Nuovi campi aziendali\n        ragione_sociale: '',\n        indirizzo: '',\n        nazione: '',\n        email: '',\n        vat: '',\n        referente_aziendale: ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Inizializza il form con i dati dell'utente se presente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserForm.useEffect\": ()=>{\n            if (user) {\n                setFormData({\n                    username: user.username || '',\n                    password: '',\n                    ruolo: user.ruolo || 'user',\n                    data_scadenza: user.data_scadenza ? user.data_scadenza.split('T')[0] : '',\n                    abilitato: user.abilitato !== undefined ? user.abilitato : true,\n                    // Nuovi campi aziendali\n                    ragione_sociale: user.ragione_sociale || '',\n                    indirizzo: user.indirizzo || '',\n                    nazione: user.nazione || '',\n                    email: user.email || '',\n                    vat: user.vat || '',\n                    referente_aziendale: user.referente_aziendale || ''\n                });\n            }\n        }\n    }[\"UserForm.useEffect\"], [\n        user\n    ]);\n    // Gestisce il cambio dei valori del form\n    const handleChange = (name, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Rimuovi l'errore per questo campo se presente\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: ''\n                }));\n        }\n    };\n    // Validazione del form\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.username.trim()) {\n            newErrors.username = 'Username è obbligatorio';\n        }\n        if (!user && !formData.password.trim()) {\n            newErrors.password = 'Password è obbligatoria per nuovi utenti';\n        }\n        if (!formData.ragione_sociale.trim()) {\n            newErrors.ragione_sociale = 'Ragione sociale è obbligatoria';\n        }\n        if (formData.email && !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = 'Email non valida';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    // Gestisce il submit del form\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setLoading(true);\n        setError('');\n        try {\n            // Prepara i dati da inviare\n            const userData = {\n                ...formData\n            };\n            // Rimuovi la password se è vuota (modifica utente)\n            if (user && !userData.password.trim()) {\n                delete userData.password;\n            }\n            // Converti la data in formato ISO se presente\n            if (userData.data_scadenza) {\n                userData.data_scadenza = userData.data_scadenza;\n            }\n            let result;\n            if (user) {\n                // Aggiorna l'utente esistente\n                result = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.usersApi.updateUser(user.id_utente, userData);\n            } else {\n                // Crea un nuovo utente\n                result = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.usersApi.createUser(userData);\n            }\n            onSave(result);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || err.message || 'Errore durante il salvataggio dell\\'utente');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    children: user ? \"Modifica Utente: \".concat(user.username) : 'Crea Nuovo Utente Standard'\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"username\",\n                                                children: \"Username *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"username\",\n                                                value: formData.username,\n                                                onChange: (e)=>handleChange('username', e.target.value),\n                                                disabled: loading,\n                                                className: errors.username ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"password\",\n                                                children: user ? 'Nuova Password (lascia vuoto per non modificare)' : 'Password *'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                value: formData.password,\n                                                onChange: (e)=>handleChange('password', e.target.value),\n                                                disabled: loading,\n                                                className: errors.password ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.password\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"ragione_sociale\",\n                                                children: \"Ragione Sociale *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"ragione_sociale\",\n                                                value: formData.ragione_sociale,\n                                                onChange: (e)=>handleChange('ragione_sociale', e.target.value),\n                                                disabled: loading,\n                                                className: errors.ragione_sociale ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.ragione_sociale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.ragione_sociale\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 42\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                value: formData.email,\n                                                onChange: (e)=>handleChange('email', e.target.value),\n                                                disabled: loading,\n                                                className: errors.email ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 32\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"indirizzo\",\n                                                children: \"Indirizzo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"indirizzo\",\n                                                value: formData.indirizzo,\n                                                onChange: (e)=>handleChange('indirizzo', e.target.value),\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"nazione\",\n                                                children: \"Nazione\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"nazione\",\n                                                value: formData.nazione,\n                                                onChange: (e)=>handleChange('nazione', e.target.value),\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"vat\",\n                                                children: \"VAT\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"vat\",\n                                                value: formData.vat,\n                                                onChange: (e)=>handleChange('vat', e.target.value),\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"referente_aziendale\",\n                                                children: \"Referente Aziendale\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"referente_aziendale\",\n                                                value: formData.referente_aziendale,\n                                                onChange: (e)=>handleChange('referente_aziendale', e.target.value),\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"data_scadenza\",\n                                                children: \"Data Scadenza\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"data_scadenza\",\n                                                type: \"date\",\n                                                value: formData.data_scadenza,\n                                                onChange: (e)=>handleChange('data_scadenza', e.target.value),\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"ruolo\",\n                                                children: \"Ruolo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: formData.ruolo,\n                                                onValueChange: (value)=>handleChange('ruolo', value),\n                                                disabled: loading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"user\",\n                                                                children: \"User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"cantieri_user\",\n                                                                children: \"Cantieri User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__.Checkbox, {\n                                        id: \"abilitato\",\n                                        checked: formData.abilitato,\n                                        onCheckedChange: (checked)=>handleChange('abilitato', checked),\n                                        disabled: loading || user && user.ruolo === 'owner'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"abilitato\",\n                                        children: \"Utente abilitato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: onCancel,\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Annulla\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        children: [\n                                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            loading ? 'Salvataggio...' : 'Salva'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\UserForm.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(UserForm, \"TW7M8rKbFh68/iM5OJSB18q/V3U=\");\n_c = UserForm;\nvar _c;\n$RefreshReg$(_c, \"UserForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/UserForm.tsx\n"));

/***/ })

});