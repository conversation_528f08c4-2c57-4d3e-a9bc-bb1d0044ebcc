"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/ResetDatabase.tsx":
/*!************************************************!*\
  !*** ./src/components/admin/ResetDatabase.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResetDatabase)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,RotateCcw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ResetDatabase() {\n    _s();\n    const [confirmText, setConfirmText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmChecked, setConfirmChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleReset = async ()=>{\n        if (confirmText !== 'RESET DATABASE' || !confirmChecked) {\n            setError('Conferma richiesta per procedere con il reset');\n            return;\n        }\n        setLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_7__.usersApi.resetDatabase();\n            setSuccess('Database resettato con successo! Tutti i dati sono stati eliminati.');\n            setConfirmText('');\n            setConfirmChecked(false);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || err.message || 'Errore durante il reset del database');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const isResetEnabled = confirmText === 'RESET DATABASE' && confirmChecked && !loading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    className: \"flex items-center gap-2 text-red-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        \"Reset Database\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-600 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-red-900 text-lg\",\n                                            children: \"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-red-700 mt-2 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Questa operazione eliminer\\xe0 PERMANENTEMENTE tutti i dati dal database:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-disc list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutti gli utenti (eccetto l'amministratore principale)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 64,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutti i cantieri e i progetti\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 65,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutti i cavi installati\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutte le bobine del parco cavi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutti i comandi e le certificazioni\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Tutti i report e i dati di produttivit\\xe0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-bold text-red-800 mt-3\",\n                                                    children: \"NON \\xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this),\n                    success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-600\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 border-t pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-slate-900 mb-4\",\n                                        children: \"Conferma Reset Database\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-600 mb-4\",\n                                        children: \"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"confirm-text\",\n                                                className: \"text-sm font-medium\",\n                                                children: [\n                                                    \"1. Digita esattamente: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold\",\n                                                        children: \"RESET DATABASE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 40\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"confirm-text\",\n                                                value: confirmText,\n                                                onChange: (e)=>setConfirmText(e.target.value),\n                                                placeholder: \"Digita: RESET DATABASE\",\n                                                disabled: loading,\n                                                className: confirmText === 'RESET DATABASE' ? 'border-green-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                id: \"confirm-checkbox\",\n                                                checked: confirmChecked,\n                                                onCheckedChange: setConfirmChecked,\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"confirm-checkbox\",\n                                                className: \"text-sm leading-relaxed\",\n                                                children: \"2. Confermo di aver compreso che questa operazione eliminer\\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-50 border border-slate-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"font-medium text-slate-900 mb-2\",\n                                        children: \"Stato Conferma:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full \".concat(confirmText === 'RESET DATABASE' ? 'bg-green-500' : 'bg-red-500')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Testo di conferma: \",\n                                                            confirmText === 'RESET DATABASE' ? '✓ Corretto' : '✗ Richiesto'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full \".concat(confirmChecked ? 'bg-green-500' : 'bg-red-500')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Checkbox confermata: \",\n                                                            confirmChecked ? '✓ Sì' : '✗ Richiesta'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleReset,\n                                disabled: !isResetEnabled,\n                                variant: \"destructive\",\n                                className: \"w-full\",\n                                size: \"lg\",\n                                children: [\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_RotateCcw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    loading ? 'Reset in corso...' : 'RESET DATABASE - ELIMINA TUTTI I DATI'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            !isResetEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-sm text-slate-500\",\n                                children: \"Completa tutti i passaggi di conferma per abilitare il reset\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: \"font-medium text-blue-900 mb-2\",\n                                children: \"Informazioni Tecniche:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-blue-700 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Il reset manterr\\xe0 la struttura delle tabelle\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• L'utente amministratore principale verr\\xe0 ricreato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Le configurazioni di sistema verranno ripristinate ai valori di default\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• L'operazione pu\\xf2 richiedere alcuni minuti per completarsi\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\admin\\\\ResetDatabase.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(ResetDatabase, \"UeHtS2ZDBt7D8K2doAYKuR9de3c=\");\n_c = ResetDatabase;\nvar _c;\n$RefreshReg$(_c, \"ResetDatabase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FkbWluL1Jlc2V0RGF0YWJhc2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0M7QUFDK0M7QUFDaEM7QUFFRjtBQUNBO0FBQ007QUFDZjtBQUNvQztBQUV6RCxTQUFTYzs7SUFDdEIsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNpQixnQkFBZ0JDLGtCQUFrQixHQUFHbEIsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDbUIsU0FBU0MsV0FBVyxHQUFHcEIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDcUIsT0FBT0MsU0FBUyxHQUFHdEIsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDdUIsU0FBU0MsV0FBVyxHQUFHeEIsK0NBQVFBLENBQUM7SUFFdkMsTUFBTXlCLGNBQWM7UUFDbEIsSUFBSVYsZ0JBQWdCLG9CQUFvQixDQUFDRSxnQkFBZ0I7WUFDdkRLLFNBQVM7WUFDVDtRQUNGO1FBRUFGLFdBQVc7UUFDWEUsU0FBUztRQUNURSxXQUFXO1FBRVgsSUFBSTtZQUNGLE1BQU1mLDhDQUFRQSxDQUFDaUIsYUFBYTtZQUM1QkYsV0FBVztZQUNYUixlQUFlO1lBQ2ZFLGtCQUFrQjtRQUNwQixFQUFFLE9BQU9TLEtBQVU7Z0JBQ1JBLG9CQUFBQTtZQUFUTCxTQUFTSyxFQUFBQSxnQkFBQUEsSUFBSUMsUUFBUSxjQUFaRCxxQ0FBQUEscUJBQUFBLGNBQWNFLElBQUksY0FBbEJGLHlDQUFBQSxtQkFBb0JHLE1BQU0sS0FBSUgsSUFBSUksT0FBTyxJQUFJO1FBQ3hELFNBQVU7WUFDUlgsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNWSxpQkFBaUJqQixnQkFBZ0Isb0JBQW9CRSxrQkFBa0IsQ0FBQ0U7SUFFOUUscUJBQ0UsOERBQUNsQixxREFBSUE7OzBCQUNILDhEQUFDRSwyREFBVUE7MEJBQ1QsNEVBQUNDLDBEQUFTQTtvQkFBQzZCLFdBQVU7O3NDQUNuQiw4REFBQ3RCLGtIQUFTQTs0QkFBQ3NCLFdBQVU7Ozs7Ozt3QkFBWTs7Ozs7Ozs7Ozs7OzBCQUlyQyw4REFBQy9CLDREQUFXQTtnQkFBQytCLFdBQVU7O2tDQUVyQiw4REFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ3JCLGtIQUFhQTtvQ0FBQ3FCLFdBQVU7Ozs7Ozs4Q0FDekIsOERBQUNDOztzREFDQyw4REFBQ0M7NENBQUdGLFdBQVU7c0RBQWlDOzs7Ozs7c0RBQy9DLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNHO29EQUFFSCxXQUFVOzhEQUFjOzs7Ozs7OERBRzNCLDhEQUFDSTtvREFBR0osV0FBVTs7c0VBQ1osOERBQUNLO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7Ozs7Ozs7OERBRU4sOERBQUNGO29EQUFFSCxXQUFVOzhEQUE4Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBU2xEWix1QkFDQyw4REFBQ2E7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNHOzRCQUFFSCxXQUFVO3NDQUFnQlo7Ozs7Ozs7Ozs7O29CQUloQ0UseUJBQ0MsOERBQUNXO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDRzs0QkFBRUgsV0FBVTtzQ0FBa0JWOzs7Ozs7Ozs7OztrQ0FLbkMsOERBQUNXO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7O2tEQUNDLDhEQUFDQzt3Q0FBR0YsV0FBVTtrREFBb0M7Ozs7OztrREFHbEQsOERBQUNHO3dDQUFFSCxXQUFVO2tEQUE4Qjs7Ozs7Ozs7Ozs7OzBDQUs3Qyw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUViLDhEQUFDQzt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUMxQix1REFBS0E7Z0RBQUNnQyxTQUFRO2dEQUFlTixXQUFVOztvREFBc0I7a0VBQ3JDLDhEQUFDTzt3REFBS1AsV0FBVTtrRUFBd0Q7Ozs7Ozs7Ozs7OzswREFFakcsOERBQUMzQix1REFBS0E7Z0RBQ0ptQyxJQUFHO2dEQUNIQyxPQUFPM0I7Z0RBQ1A0QixVQUFVLENBQUNDLElBQU01QixlQUFlNEIsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO2dEQUM5Q0ksYUFBWTtnREFDWkMsVUFBVTVCO2dEQUNWYyxXQUFXbEIsZ0JBQWdCLG1CQUFtQixxQkFBcUI7Ozs7Ozs7Ozs7OztrREFLdkUsOERBQUNtQjt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUN6Qiw2REFBUUE7Z0RBQ1BpQyxJQUFHO2dEQUNITyxTQUFTL0I7Z0RBQ1RnQyxpQkFBaUIvQjtnREFDakI2QixVQUFVNUI7Ozs7OzswREFFWiw4REFBQ1osdURBQUtBO2dEQUFDZ0MsU0FBUTtnREFBbUJOLFdBQVU7MERBQTBCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBUTFFLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNpQjt3Q0FBR2pCLFdBQVU7a0RBQWtDOzs7Ozs7a0RBQ2hELDhEQUFDQzt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUlELFdBQVcsd0JBRWYsT0FEQ2xCLGdCQUFnQixtQkFBbUIsaUJBQWlCOzs7Ozs7a0VBRXRELDhEQUFDb0M7OzREQUFLOzREQUFvQnBDLGdCQUFnQixtQkFBbUIsZUFBZTs7Ozs7Ozs7Ozs7OzswREFFOUUsOERBQUNtQjtnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUFJRCxXQUFXLHdCQUVmLE9BRENoQixpQkFBaUIsaUJBQWlCOzs7Ozs7a0VBRXBDLDhEQUFDa0M7OzREQUFLOzREQUFzQmxDLGlCQUFpQixTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU01RCw4REFBQ1oseURBQU1BO2dDQUNMK0MsU0FBUzNCO2dDQUNUc0IsVUFBVSxDQUFDZjtnQ0FDWHFCLFNBQVE7Z0NBQ1JwQixXQUFVO2dDQUNWcUIsTUFBSzs7b0NBRUpuQyx3QkFDQyw4REFBQ1QsbUhBQU9BO3dDQUFDdUIsV0FBVTs7Ozs7NkRBRW5CLDhEQUFDcEIsbUhBQU1BO3dDQUFDb0IsV0FBVTs7Ozs7O29DQUVuQmQsVUFBVSxzQkFBc0I7Ozs7Ozs7NEJBR2xDLENBQUNhLGdDQUNBLDhEQUFDSTtnQ0FBRUgsV0FBVTswQ0FBcUM7Ozs7Ozs7Ozs7OztrQ0FPdEQsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ2lCO2dDQUFHakIsV0FBVTswQ0FBaUM7Ozs7OzswQ0FDL0MsOERBQUNJO2dDQUFHSixXQUFVOztrREFDWiw4REFBQ0s7a0RBQUc7Ozs7OztrREFDSiw4REFBQ0E7a0RBQUc7Ozs7OztrREFDSiw4REFBQ0E7a0RBQUc7Ozs7OztrREFDSiw4REFBQ0E7a0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1oQjtHQWhMd0J4QjtLQUFBQSIsInNvdXJjZXMiOlsiQzpcXENNU1xcd2ViYXBwLW5leHRqc1xcc3JjXFxjb21wb25lbnRzXFxhZG1pblxcUmVzZXREYXRhYmFzZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgRGFuZ2VyQnV0dG9uLCBTZWNvbmRhcnlCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYW5pbWF0ZWQtYnV0dG9uJ1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9sYWJlbCdcbmltcG9ydCB7IENoZWNrYm94IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NoZWNrYm94J1xuaW1wb3J0IHsgdXNlcnNBcGkgfSBmcm9tICdAL2xpYi9hcGknXG5pbXBvcnQgeyBMb2FkZXIyLCBSb3RhdGVDY3csIEFsZXJ0VHJpYW5nbGUsIFRyYXNoMiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUmVzZXREYXRhYmFzZSgpIHtcbiAgY29uc3QgW2NvbmZpcm1UZXh0LCBzZXRDb25maXJtVGV4dF0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2NvbmZpcm1DaGVja2VkLCBzZXRDb25maXJtQ2hlY2tlZF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFtzdWNjZXNzLCBzZXRTdWNjZXNzXSA9IHVzZVN0YXRlKCcnKVxuXG4gIGNvbnN0IGhhbmRsZVJlc2V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmIChjb25maXJtVGV4dCAhPT0gJ1JFU0VUIERBVEFCQVNFJyB8fCAhY29uZmlybUNoZWNrZWQpIHtcbiAgICAgIHNldEVycm9yKCdDb25mZXJtYSByaWNoaWVzdGEgcGVyIHByb2NlZGVyZSBjb24gaWwgcmVzZXQnKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgc2V0TG9hZGluZyh0cnVlKVxuICAgIHNldEVycm9yKCcnKVxuICAgIHNldFN1Y2Nlc3MoJycpXG5cbiAgICB0cnkge1xuICAgICAgYXdhaXQgdXNlcnNBcGkucmVzZXREYXRhYmFzZSgpXG4gICAgICBzZXRTdWNjZXNzKCdEYXRhYmFzZSByZXNldHRhdG8gY29uIHN1Y2Nlc3NvISBUdXR0aSBpIGRhdGkgc29ubyBzdGF0aSBlbGltaW5hdGkuJylcbiAgICAgIHNldENvbmZpcm1UZXh0KCcnKVxuICAgICAgc2V0Q29uZmlybUNoZWNrZWQoZmFsc2UpXG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcbiAgICAgIHNldEVycm9yKGVyci5yZXNwb25zZT8uZGF0YT8uZGV0YWlsIHx8IGVyci5tZXNzYWdlIHx8ICdFcnJvcmUgZHVyYW50ZSBpbCByZXNldCBkZWwgZGF0YWJhc2UnKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGlzUmVzZXRFbmFibGVkID0gY29uZmlybVRleHQgPT09ICdSRVNFVCBEQVRBQkFTRScgJiYgY29uZmlybUNoZWNrZWQgJiYgIWxvYWRpbmdcblxuICByZXR1cm4gKFxuICAgIDxDYXJkPlxuICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1yZWQtNjAwXCI+XG4gICAgICAgICAgPFJvdGF0ZUNjdyBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICBSZXNldCBEYXRhYmFzZVxuICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgey8qIEF2dmlzbyBkaSBwZXJpY29sbyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0zXCI+XG4gICAgICAgICAgICA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtcmVkLTYwMCBtdC0wLjVcIiAvPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LXJlZC05MDAgdGV4dC1sZ1wiPuKaoO+4jyBBVFRFTlpJT05FIC0gT1BFUkFaSU9ORSBJUlJFVkVSU0lCSUxFPC9oND5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC03MDAgbXQtMiBzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgUXVlc3RhIG9wZXJhemlvbmUgZWxpbWluZXLDoCBQRVJNQU5FTlRFTUVOVEUgdHV0dGkgaSBkYXRpIGRhbCBkYXRhYmFzZTpcbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cImxpc3QtZGlzYyBsaXN0LWluc2lkZSBzcGFjZS15LTEgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgPGxpPlR1dHRpIGdsaSB1dGVudGkgKGVjY2V0dG8gbCdhbW1pbmlzdHJhdG9yZSBwcmluY2lwYWxlKTwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+VHV0dGkgaSBjYW50aWVyaSBlIGkgcHJvZ2V0dGk8L2xpPlxuICAgICAgICAgICAgICAgICAgPGxpPlR1dHRpIGkgY2F2aSBpbnN0YWxsYXRpPC9saT5cbiAgICAgICAgICAgICAgICAgIDxsaT5UdXR0ZSBsZSBib2JpbmUgZGVsIHBhcmNvIGNhdmk8L2xpPlxuICAgICAgICAgICAgICAgICAgPGxpPlR1dHRpIGkgY29tYW5kaSBlIGxlIGNlcnRpZmljYXppb25pPC9saT5cbiAgICAgICAgICAgICAgICAgIDxsaT5UdXR0aSBpIHJlcG9ydCBlIGkgZGF0aSBkaSBwcm9kdXR0aXZpdMOgPC9saT5cbiAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LXJlZC04MDAgbXQtM1wiPlxuICAgICAgICAgICAgICAgICAgTk9OIMOIIFBPU1NJQklMRSBSRUNVUEVSQVJFIEkgREFUSSBET1BPIElMIFJFU0VUIVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE1lc3NhZ2dpIGRpIHN0YXRvICovfVxuICAgICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwXCI+e2Vycm9yfTwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7c3VjY2VzcyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDBcIj57c3VjY2Vzc308L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIEZvcm0gZGkgY29uZmVybWEgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00IGJvcmRlci10IHB0LTZcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1zbGF0ZS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICBDb25mZXJtYSBSZXNldCBEYXRhYmFzZVxuICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zbGF0ZS02MDAgbWItNFwiPlxuICAgICAgICAgICAgICBQZXIgcHJvY2VkZXJlIGNvbiBpbCByZXNldCwgZGV2aSBjb25mZXJtYXJlIGwnb3BlcmF6aW9uZSBzZWd1ZW5kbyBxdWVzdGkgcGFzc2FnZ2k6XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgey8qIFN0ZXAgMTogRGlnaXRhcmUgdGVzdG8gZGkgY29uZmVybWEgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNvbmZpcm0tdGV4dFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAxLiBEaWdpdGEgZXNhdHRhbWVudGU6IDxjb2RlIGNsYXNzTmFtZT1cImJnLXNsYXRlLTEwMCBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXJlZC02MDAgZm9udC1ib2xkXCI+UkVTRVQgREFUQUJBU0U8L2NvZGU+XG4gICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgIGlkPVwiY29uZmlybS10ZXh0XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Y29uZmlybVRleHR9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb25maXJtVGV4dChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJEaWdpdGE6IFJFU0VUIERBVEFCQVNFXCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NvbmZpcm1UZXh0ID09PSAnUkVTRVQgREFUQUJBU0UnID8gJ2JvcmRlci1ncmVlbi01MDAnIDogJyd9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFN0ZXAgMjogQ2hlY2tib3ggZGkgY29uZmVybWEgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgIDxDaGVja2JveFxuICAgICAgICAgICAgICAgIGlkPVwiY29uZmlybS1jaGVja2JveFwiXG4gICAgICAgICAgICAgICAgY2hlY2tlZD17Y29uZmlybUNoZWNrZWR9XG4gICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXtzZXRDb25maXJtQ2hlY2tlZH1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjb25maXJtLWNoZWNrYm94XCIgY2xhc3NOYW1lPVwidGV4dC1zbSBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICAyLiBDb25mZXJtbyBkaSBhdmVyIGNvbXByZXNvIGNoZSBxdWVzdGEgb3BlcmF6aW9uZSBlbGltaW5lcsOgIFRVVFRJIGkgZGF0aSBkYWwgZGF0YWJhc2UgXG4gICAgICAgICAgICAgICAgaW4gbW9kbyBQRVJNQU5FTlRFIGUgSVJSRVZFUlNJQklMRS4gSG8gZWZmZXR0dWF0byB1biBiYWNrdXAgc2UgbmVjZXNzYXJpby5cbiAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFN0YXRvIGRpIGNvbmZlcm1hICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc2xhdGUtNTAgYm9yZGVyIGJvcmRlci1zbGF0ZS0yMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNsYXRlLTkwMCBtYi0yXCI+U3RhdG8gQ29uZmVybWE6PC9oNT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgIGNvbmZpcm1UZXh0ID09PSAnUkVTRVQgREFUQUJBU0UnID8gJ2JnLWdyZWVuLTUwMCcgOiAnYmctcmVkLTUwMCdcbiAgICAgICAgICAgICAgICB9YH0+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHNwYW4+VGVzdG8gZGkgY29uZmVybWE6IHtjb25maXJtVGV4dCA9PT0gJ1JFU0VUIERBVEFCQVNFJyA/ICfinJMgQ29ycmV0dG8nIDogJ+KclyBSaWNoaWVzdG8nfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMyBoLTMgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICBjb25maXJtQ2hlY2tlZCA/ICdiZy1ncmVlbi01MDAnIDogJ2JnLXJlZC01MDAnXG4gICAgICAgICAgICAgICAgfWB9PjwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuPkNoZWNrYm94IGNvbmZlcm1hdGE6IHtjb25maXJtQ2hlY2tlZCA/ICfinJMgU8OsJyA6ICfinJcgUmljaGllc3RhJ308L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUHVsc2FudGUgZGkgcmVzZXQgKi99XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUmVzZXR9XG4gICAgICAgICAgICBkaXNhYmxlZD17IWlzUmVzZXRFbmFibGVkfVxuICAgICAgICAgICAgdmFyaWFudD1cImRlc3RydWN0aXZlXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtsb2FkaW5nID8gKFxuICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICB7bG9hZGluZyA/ICdSZXNldCBpbiBjb3Jzby4uLicgOiAnUkVTRVQgREFUQUJBU0UgLSBFTElNSU5BIFRVVFRJIEkgREFUSSd9XG4gICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICB7IWlzUmVzZXRFbmFibGVkICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtc20gdGV4dC1zbGF0ZS01MDBcIj5cbiAgICAgICAgICAgICAgQ29tcGxldGEgdHV0dGkgaSBwYXNzYWdnaSBkaSBjb25mZXJtYSBwZXIgYWJpbGl0YXJlIGlsIHJlc2V0XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEluZm9ybWF6aW9uaSBhZ2dpdW50aXZlICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLWxnIHAtNCB0ZXh0LXNtXCI+XG4gICAgICAgICAgPGg1IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtYmx1ZS05MDAgbWItMlwiPkluZm9ybWF6aW9uaSBUZWNuaWNoZTo8L2g1PlxuICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNzAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgPGxpPuKAoiBJbCByZXNldCBtYW50ZXJyw6AgbGEgc3RydXR0dXJhIGRlbGxlIHRhYmVsbGU8L2xpPlxuICAgICAgICAgICAgPGxpPuKAoiBMJ3V0ZW50ZSBhbW1pbmlzdHJhdG9yZSBwcmluY2lwYWxlIHZlcnLDoCByaWNyZWF0bzwvbGk+XG4gICAgICAgICAgICA8bGk+4oCiIExlIGNvbmZpZ3VyYXppb25pIGRpIHNpc3RlbWEgdmVycmFubm8gcmlwcmlzdGluYXRlIGFpIHZhbG9yaSBkaSBkZWZhdWx0PC9saT5cbiAgICAgICAgICAgIDxsaT7igKIgTCdvcGVyYXppb25lIHB1w7IgcmljaGllZGVyZSBhbGN1bmkgbWludXRpIHBlciBjb21wbGV0YXJzaTwvbGk+XG4gICAgICAgICAgPC91bD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmRDb250ZW50PlxuICAgIDwvQ2FyZD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIklucHV0IiwiTGFiZWwiLCJDaGVja2JveCIsInVzZXJzQXBpIiwiTG9hZGVyMiIsIlJvdGF0ZUNjdyIsIkFsZXJ0VHJpYW5nbGUiLCJUcmFzaDIiLCJSZXNldERhdGFiYXNlIiwiY29uZmlybVRleHQiLCJzZXRDb25maXJtVGV4dCIsImNvbmZpcm1DaGVja2VkIiwic2V0Q29uZmlybUNoZWNrZWQiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJzdWNjZXNzIiwic2V0U3VjY2VzcyIsImhhbmRsZVJlc2V0IiwicmVzZXREYXRhYmFzZSIsImVyciIsInJlc3BvbnNlIiwiZGF0YSIsImRldGFpbCIsIm1lc3NhZ2UiLCJpc1Jlc2V0RW5hYmxlZCIsImNsYXNzTmFtZSIsImRpdiIsImg0IiwicCIsInVsIiwibGkiLCJodG1sRm9yIiwiY29kZSIsImlkIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsImRpc2FibGVkIiwiY2hlY2tlZCIsIm9uQ2hlY2tlZENoYW5nZSIsImg1Iiwic3BhbiIsIm9uQ2xpY2siLCJ2YXJpYW50Iiwic2l6ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/ResetDatabase.tsx\n"));

/***/ })

});