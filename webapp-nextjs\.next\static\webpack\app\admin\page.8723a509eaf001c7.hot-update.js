"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/animated-button */ \"(app-pages-browser)/./src/components/ui/animated-button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_admin_UserForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/UserForm */ \"(app-pages-browser)/./src/components/admin/UserForm.tsx\");\n/* harmony import */ var _components_admin_ImpersonateUser__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/ImpersonateUser */ \"(app-pages-browser)/./src/components/admin/ImpersonateUser.tsx\");\n/* harmony import */ var _components_admin_DatabaseView__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/DatabaseView */ \"(app-pages-browser)/./src/components/admin/DatabaseView.tsx\");\n/* harmony import */ var _components_admin_ResetDatabase__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/admin/ResetDatabase */ \"(app-pages-browser)/./src/components/admin/ResetDatabase.tsx\");\n/* harmony import */ var _components_admin_TipologieCaviManager__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/admin/TipologieCaviManager */ \"(app-pages-browser)/./src/components/admin/TipologieCaviManager.tsx\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cable.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('visualizza-utenti');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cantieri, setCantieri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [notification, setNotification] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        message: '',\n        severity: 'success'\n    });\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    // Carica dati dal backend\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            loadData();\n        }\n    }[\"AdminPage.useEffect\"], [\n        activeTab\n    ]);\n    const loadData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError('');\n            console.log('Caricamento dati per tab:', activeTab);\n            console.log('Token presente:',  true ? localStorage.getItem('token') : 0);\n            console.log('Utente corrente:', user);\n            if (activeTab === 'visualizza-utenti' || activeTab === 'crea-utente' || activeTab === 'accedi-come-utente') {\n                console.log('Chiamata API per ottenere utenti...');\n                const usersData = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.usersApi.getUsers();\n                console.log('Utenti ricevuti:', usersData);\n                setUsers(usersData);\n            } else if (activeTab === 'cantieri') {\n                const cantieriData = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.cantieriApi.getCantieri();\n                setCantieri(cantieriData);\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Errore caricamento dati:', error);\n            console.error('Dettagli errore:', error.response);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || error.message || 'Errore durante il caricamento dei dati');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEditUser = (userToEdit)=>{\n        setSelectedUser(userToEdit);\n        setActiveTab('modifica-utente');\n    };\n    const handleToggleUserStatus = async (userId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_8__.usersApi.toggleUserStatus(userId);\n            loadData() // Ricarica i dati\n            ;\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Errore toggle status:', error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || 'Errore durante la modifica dello stato utente');\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (confirm('Sei sicuro di voler eliminare questo utente?')) {\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.usersApi.deleteUser(userId);\n                loadData() // Ricarica i dati\n                ;\n            } catch (error) {\n                var _error_response_data, _error_response;\n                console.error('Errore eliminazione utente:', error);\n                setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || 'Errore durante l\\'eliminazione dell\\'utente');\n            }\n        }\n    };\n    const handleSaveUser = (savedUser)=>{\n        console.log('Utente salvato:', savedUser);\n        setSelectedUser(null);\n        setActiveTab('visualizza-utenti');\n        loadData() // Ricarica i dati\n        ;\n    };\n    const handleCancelForm = ()=>{\n        setSelectedUser(null);\n        setActiveTab('visualizza-utenti');\n    };\n    // Helper functions per i badge\n    const getRuoloBadge = (ruolo)=>{\n        switch(ruolo){\n            case 'owner':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-purple-100 text-purple-800\",\n                    children: \"Owner\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, this);\n            case 'user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, this);\n            case 'cantieri_user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"Cantieri User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    children: ruolo\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (abilitato, data_scadenza)=>{\n        if (!abilitato) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                className: \"bg-red-100 text-red-800\",\n                children: \"Disabilitato\"\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 14\n            }, this);\n        }\n        if (data_scadenza) {\n            const scadenza = new Date(data_scadenza);\n            const oggi = new Date();\n            if (scadenza < oggi) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-red-100 text-red-800\",\n                    children: \"Scaduto\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 16\n                }, this);\n            } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"In Scadenza\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 16\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n            className: \"bg-green-100 text-green-800\",\n            children: \"Attivo\"\n        }, void 0, false, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 12\n        }, this);\n    };\n    const filteredUsers = users.filter((u)=>{\n        var _u_username, _u_ragione_sociale, _u_email;\n        return ((_u_username = u.username) === null || _u_username === void 0 ? void 0 : _u_username.toLowerCase().includes(searchTerm.toLowerCase())) || ((_u_ragione_sociale = u.ragione_sociale) === null || _u_ragione_sociale === void 0 ? void 0 : _u_ragione_sociale.toLowerCase().includes(searchTerm.toLowerCase())) || ((_u_email = u.email) === null || _u_email === void 0 ? void 0 : _u_email.toLowerCase().includes(searchTerm.toLowerCase()));\n    });\n    // Verifica se l'utente ha permessi di amministrazione\n    if ((user === null || user === void 0 ? void 0 : user.ruolo) !== 'owner') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-slate-900 mb-2\",\n                            children: \"Accesso Negato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-slate-600\",\n                            children: \"Non hai i permessi necessari per accedere a questa sezione.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-slate-900 flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Pannello Admin\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 mt-1\",\n                                    children: \"Questa sezione mostra la lista di tutti gli utenti del sistema.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        activeTab === 'crea-utente' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__.PrimaryButton, {\n                            size: \"sm\",\n                            onClick: ()=>setActiveTab('visualizza-utenti'),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 21\n                            }, void 0),\n                            glow: true,\n                            children: \"Aggiorna\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full \".concat(selectedUser ? 'grid-cols-7' : 'grid-cols-6'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"visualizza-utenti\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Visualizza Utenti\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"crea-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Crea Nuovo Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"modifica-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Modifica Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"accedi-come-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Accedi come Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"database-tipologie-cavi\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Database Tipologie Cavi\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"visualizza-database-raw\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Visualizza Database Raw\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"reset-database\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Reset Database\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"visualizza-utenti\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-slate-900\",\n                                                    children: \"Visualizza Utenti\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600\",\n                                                    children: \"Questa sezione mostra la lista di tutti gli utenti del sistema.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__.PrimaryButton, {\n                                            onClick: loadData,\n                                            loading: isLoading,\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            size: \"sm\",\n                                            children: \"Aggiorna\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Lista Utenti\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-md border\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Username\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Ruolo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Ragione Sociale\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"VAT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Nazione\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Referente\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Scadenza\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Stato\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Azioni\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableBody, {\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    colSpan: 12,\n                                                                    className: \"text-center py-8\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4 animate-spin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 292,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Caricamento...\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 25\n                                                            }, this) : users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    colSpan: 12,\n                                                                    className: \"text-center py-8 text-slate-500\",\n                                                                    children: \"Nessun utente trovato\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 25\n                                                            }, this) : users.map((utente)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.id_utente\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            className: \"font-medium\",\n                                                                            children: utente.username\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.password_plain || '***'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 308,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: getRuoloBadge(utente.ruolo)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.ragione_sociale || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.email || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.vat || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 312,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.nazione || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.referente_aziendale || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.data_scadenza ? new Date(utente.data_scadenza).toLocaleDateString('it-IT') : 'N/A'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: getStatusBadge(utente.abilitato, utente.data_scadenza)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__.OutlineButton, {\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleEditUser(utente),\n                                                                                        title: \"Modifica\",\n                                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 328,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        children: \"Modifica\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 324,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__.AnimatedButton, {\n                                                                                        variant: utente.abilitato ? 'danger' : 'success',\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleToggleUserStatus(utente.id_utente),\n                                                                                        title: utente.abilitato ? 'Disabilita' : 'Abilita',\n                                                                                        disabled: utente.ruolo === 'owner',\n                                                                                        icon: utente.abilitato ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 338,\n                                                                                            columnNumber: 60\n                                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 338,\n                                                                                            columnNumber: 92\n                                                                                        }, void 0),\n                                                                                        children: utente.abilitato ? 'Disabilita' : 'Abilita'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 332,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_button__WEBPACK_IMPORTED_MODULE_3__.DangerButton, {\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleDeleteUser(utente.id_utente),\n                                                                                        title: \"Elimina\",\n                                                                                        disabled: utente.ruolo === 'owner',\n                                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 347,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        children: \"Elimina\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 342,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 323,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, utente.id_utente, true, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"crea-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Crea Nuovo Utente Standard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi creare un nuovo utente standard nel sistema.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UserForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    user: null,\n                                    onSave: handleSaveUser,\n                                    onCancel: handleCancelForm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this),\n                        selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"modifica-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: [\n                                                \"Modifica Utente: \",\n                                                selectedUser.username\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi modificare i dati dell'utente selezionato.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UserForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    user: selectedUser,\n                                    onSave: handleSaveUser,\n                                    onCancel: handleCancelForm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"accedi-come-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Accedi come Utente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi accedere al sistema impersonando un altro utente.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ImpersonateUser__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"database-tipologie-cavi\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Database Tipologie Cavi\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Gestisci il database enciclopedico delle tipologie di cavi: categorie, produttori, standard e tipologie.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_TipologieCaviManager__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"visualizza-database-raw\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Visualizzazione Database Raw\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Questa sezione mostra una visualizzazione raw del database. Puoi vedere i dati delle tabelle principali.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_DatabaseView__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"reset-database\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Reset Database\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Attenzione: questa operazione canceller\\xe0 tutti i dati del database.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ResetDatabase__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"WAwpFRDbUhtFhmQPH9w/cXXy7RA=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});