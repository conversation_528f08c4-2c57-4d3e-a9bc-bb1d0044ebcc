"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_admin_UserForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/UserForm */ \"(app-pages-browser)/./src/components/admin/UserForm.tsx\");\n/* harmony import */ var _components_admin_ImpersonateUser__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/ImpersonateUser */ \"(app-pages-browser)/./src/components/admin/ImpersonateUser.tsx\");\n/* harmony import */ var _components_admin_DatabaseView__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/DatabaseView */ \"(app-pages-browser)/./src/components/admin/DatabaseView.tsx\");\n/* harmony import */ var _components_admin_ResetDatabase__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/admin/ResetDatabase */ \"(app-pages-browser)/./src/components/admin/ResetDatabase.tsx\");\n/* harmony import */ var _components_admin_TipologieCaviManager__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/admin/TipologieCaviManager */ \"(app-pages-browser)/./src/components/admin/TipologieCaviManager.tsx\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cable.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Cable,CheckCircle,Clock,Database,Edit,Loader2,LogIn,RefreshCw,RotateCcw,Settings,Shield,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('visualizza-utenti');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cantieri, setCantieri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [notification, setNotification] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        message: '',\n        severity: 'success'\n    });\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    // Carica dati dal backend\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            loadData();\n        }\n    }[\"AdminPage.useEffect\"], [\n        activeTab\n    ]);\n    const loadData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError('');\n            console.log('Caricamento dati per tab:', activeTab);\n            console.log('Token presente:',  true ? localStorage.getItem('token') : 0);\n            console.log('Utente corrente:', user);\n            if (activeTab === 'visualizza-utenti' || activeTab === 'crea-utente' || activeTab === 'accedi-come-utente') {\n                console.log('Chiamata API per ottenere utenti...');\n                const usersData = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.usersApi.getUsers();\n                console.log('Utenti ricevuti:', usersData);\n                setUsers(usersData);\n            } else if (activeTab === 'cantieri') {\n                const cantieriData = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.cantieriApi.getCantieri();\n                setCantieri(cantieriData);\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Errore caricamento dati:', error);\n            console.error('Dettagli errore:', error.response);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || error.message || 'Errore durante il caricamento dei dati');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEditUser = (userToEdit)=>{\n        setSelectedUser(userToEdit);\n        setActiveTab('modifica-utente');\n    };\n    const handleToggleUserStatus = async (userId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_8__.usersApi.toggleUserStatus(userId);\n            loadData() // Ricarica i dati\n            ;\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Errore toggle status:', error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || 'Errore durante la modifica dello stato utente');\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (confirm('Sei sicuro di voler eliminare questo utente?')) {\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.usersApi.deleteUser(userId);\n                loadData() // Ricarica i dati\n                ;\n            } catch (error) {\n                var _error_response_data, _error_response;\n                console.error('Errore eliminazione utente:', error);\n                setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || 'Errore durante l\\'eliminazione dell\\'utente');\n            }\n        }\n    };\n    const handleSaveUser = (savedUser)=>{\n        console.log('Utente salvato:', savedUser);\n        setSelectedUser(null);\n        setActiveTab('visualizza-utenti');\n        loadData() // Ricarica i dati\n        ;\n    };\n    const handleCancelForm = ()=>{\n        setSelectedUser(null);\n        setActiveTab('visualizza-utenti');\n    };\n    // Helper functions per i badge\n    const getRuoloBadge = (ruolo)=>{\n        switch(ruolo){\n            case 'owner':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-purple-100 text-purple-800\",\n                    children: \"Owner\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, this);\n            case 'user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, this);\n            case 'cantieri_user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"Cantieri User\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    children: ruolo\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (abilitato, data_scadenza)=>{\n        if (!abilitato) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                className: \"bg-red-100 text-red-800\",\n                children: \"Disabilitato\"\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 14\n            }, this);\n        }\n        if (data_scadenza) {\n            const scadenza = new Date(data_scadenza);\n            const oggi = new Date();\n            if (scadenza < oggi) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-red-100 text-red-800\",\n                    children: \"Scaduto\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 16\n                }, this);\n            } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"In Scadenza\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 16\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n            className: \"bg-green-100 text-green-800\",\n            children: \"Attivo\"\n        }, void 0, false, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 12\n        }, this);\n    };\n    const filteredUsers = users.filter((u)=>{\n        var _u_username, _u_ragione_sociale, _u_email;\n        return ((_u_username = u.username) === null || _u_username === void 0 ? void 0 : _u_username.toLowerCase().includes(searchTerm.toLowerCase())) || ((_u_ragione_sociale = u.ragione_sociale) === null || _u_ragione_sociale === void 0 ? void 0 : _u_ragione_sociale.toLowerCase().includes(searchTerm.toLowerCase())) || ((_u_email = u.email) === null || _u_email === void 0 ? void 0 : _u_email.toLowerCase().includes(searchTerm.toLowerCase()));\n    });\n    // Verifica se l'utente ha permessi di amministrazione\n    if ((user === null || user === void 0 ? void 0 : user.ruolo) !== 'owner') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-slate-900 mb-2\",\n                            children: \"Accesso Negato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-slate-600\",\n                            children: \"Non hai i permessi necessari per accedere a questa sezione.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-slate-900 flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Pannello Admin\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 mt-1\",\n                                    children: \"Questa sezione mostra la lista di tutti gli utenti del sistema.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        activeTab === 'crea-utente' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            onClick: ()=>setActiveTab('visualizza-utenti'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this),\n                                \"Aggiorna\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full \".concat(selectedUser ? 'grid-cols-7' : 'grid-cols-6'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"visualizza-utenti\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Visualizza Utenti\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"crea-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Crea Nuovo Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"modifica-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Modifica Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"accedi-come-utente\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Accedi come Utente\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"database-tipologie-cavi\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Database Tipologie Cavi\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"visualizza-database-raw\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Visualizza Database Raw\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"reset-database\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Reset Database\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"visualizza-utenti\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-slate-900\",\n                                                    children: \"Visualizza Utenti\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600\",\n                                                    children: \"Questa sezione mostra la lista di tutti gli utenti del sistema.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: loadData,\n                                            disabled: isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Aggiorna\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Lista Utenti\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-md border\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Username\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Ruolo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Ragione Sociale\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"VAT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Nazione\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Referente\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Scadenza\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Stato\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                                        children: \"Azioni\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableBody, {\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    colSpan: 12,\n                                                                    className: \"text-center py-8\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4 animate-spin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Caricamento...\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 25\n                                                            }, this) : users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    colSpan: 12,\n                                                                    className: \"text-center py-8 text-slate-500\",\n                                                                    children: \"Nessun utente trovato\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 25\n                                                            }, this) : users.map((utente)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.id_utente\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 301,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            className: \"font-medium\",\n                                                                            children: utente.username\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 302,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.password_plain || '***'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: getRuoloBadge(utente.ruolo)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.ragione_sociale || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.email || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.vat || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.nazione || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 308,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.referente_aziendale || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: utente.data_scadenza ? new Date(utente.data_scadenza).toLocaleDateString('it-IT') : 'N/A'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: getStatusBadge(utente.abilitato, utente.data_scadenza)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 316,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleEditUser(utente),\n                                                                                        title: \"Modifica\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 325,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 319,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleToggleUserStatus(utente.id_utente),\n                                                                                        title: utente.abilitato ? 'Disabilita' : 'Abilita',\n                                                                                        disabled: utente.ruolo === 'owner',\n                                                                                        children: utente.abilitato ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 334,\n                                                                                            columnNumber: 55\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-red-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 334,\n                                                                                            columnNumber: 108\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 327,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleDeleteUser(utente.id_utente),\n                                                                                        title: \"Elimina\",\n                                                                                        disabled: utente.ruolo === 'owner',\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cable_CheckCircle_Clock_Database_Edit_Loader2_LogIn_RefreshCw_RotateCcw_Settings_Shield_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-red-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                            lineNumber: 343,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 336,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 318,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, utente.id_utente, true, {\n                                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"crea-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Crea Nuovo Utente Standard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi creare un nuovo utente standard nel sistema.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UserForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    user: null,\n                                    onSave: handleSaveUser,\n                                    onCancel: handleCancelForm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, this),\n                        selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"modifica-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: [\n                                                \"Modifica Utente: \",\n                                                selectedUser.username\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi modificare i dati dell'utente selezionato.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UserForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    user: selectedUser,\n                                    onSave: handleSaveUser,\n                                    onCancel: handleCancelForm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"accedi-come-utente\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Accedi come Utente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Da qui puoi accedere al sistema impersonando un altro utente.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ImpersonateUser__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"database-tipologie-cavi\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Database Tipologie Cavi\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Gestisci il database enciclopedico delle tipologie di cavi: categorie, produttori, standard e tipologie.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_TipologieCaviManager__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"visualizza-database-raw\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Visualizzazione Database Raw\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Questa sezione mostra una visualizzazione raw del database. Puoi vedere i dati delle tabelle principali.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_DatabaseView__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"reset-database\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-slate-900\",\n                                            children: \"Reset Database\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"Attenzione: questa operazione canceller\\xe0 tutti i dati del database.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ResetDatabase__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"WAwpFRDbUhtFhmQPH9w/cXXy7RA=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});