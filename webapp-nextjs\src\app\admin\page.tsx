'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { AnimatedButton, PrimaryButton, SecondaryButton, DangerButton, OutlineButton } from '@/components/ui/animated-button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAuth } from '@/contexts/AuthContext'
import { api, cantieriApi, usersApi } from '@/lib/api'
import { User, Cantiere } from '@/types'
import UserForm from '@/components/admin/UserForm'
import ImpersonateUser from '@/components/admin/ImpersonateUser'
import DatabaseView from '@/components/admin/DatabaseView'
import ResetDatabase from '@/components/admin/ResetDatabase'
import TipologieCaviManager from '@/components/admin/TipologieCaviManager'
import {
  Settings,
  Users,
  Building2,
  Search,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  Shield,
  Key,
  Loader2,
  UserPlus,
  LogIn,
  Cable,
  Database,
  RotateCcw,
  RefreshCw
} from 'lucide-react'

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState('visualizza-utenti')
  const [searchTerm, setSearchTerm] = useState('')
  const [users, setUsers] = useState<User[]>([])
  const [cantieri, setCantieri] = useState<Cantiere[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' })

  const { user } = useAuth()

  // Carica dati dal backend
  useEffect(() => {
    loadData()
  }, [activeTab])

  const loadData = async () => {
    try {
      setIsLoading(true)
      setError('')

      console.log('Caricamento dati per tab:', activeTab)
      console.log('Token presente:', typeof window !== 'undefined' ? localStorage.getItem('token') : 'N/A')
      console.log('Utente corrente:', user)

      if (activeTab === 'visualizza-utenti' || activeTab === 'crea-utente' || activeTab === 'accedi-come-utente') {
        console.log('Chiamata API per ottenere utenti...')
        const usersData = await usersApi.getUsers()
        console.log('Utenti ricevuti:', usersData)
        setUsers(usersData)
      } else if (activeTab === 'cantieri') {
        const cantieriData = await cantieriApi.getCantieri()
        setCantieri(cantieriData)
      }
    } catch (error: any) {
      console.error('Errore caricamento dati:', error)
      console.error('Dettagli errore:', error.response)
      setError(error.response?.data?.detail || error.message || 'Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditUser = (userToEdit: User) => {
    setSelectedUser(userToEdit)
    setActiveTab('modifica-utente')
  }

  const handleToggleUserStatus = async (userId: number) => {
    try {
      await usersApi.toggleUserStatus(userId)
      loadData() // Ricarica i dati
    } catch (error: any) {
      console.error('Errore toggle status:', error)
      setError(error.response?.data?.detail || 'Errore durante la modifica dello stato utente')
    }
  }

  const handleDeleteUser = async (userId: number) => {
    if (confirm('Sei sicuro di voler eliminare questo utente?')) {
      try {
        await usersApi.deleteUser(userId)
        loadData() // Ricarica i dati
      } catch (error: any) {
        console.error('Errore eliminazione utente:', error)
        setError(error.response?.data?.detail || 'Errore durante l\'eliminazione dell\'utente')
      }
    }
  }

  const handleSaveUser = (savedUser: User) => {
    console.log('Utente salvato:', savedUser)
    setSelectedUser(null)
    setActiveTab('visualizza-utenti')
    loadData() // Ricarica i dati
  }

  const handleCancelForm = () => {
    setSelectedUser(null)
    setActiveTab('visualizza-utenti')
  }

  // Helper functions per i badge

  const getRuoloBadge = (ruolo: string) => {
    switch (ruolo) {
      case 'owner':
        return <Badge className="bg-purple-100 text-purple-800">Owner</Badge>
      case 'user':
        return <Badge className="bg-blue-100 text-blue-800">User</Badge>
      case 'cantieri_user':
        return <Badge className="bg-green-100 text-green-800">Cantieri User</Badge>
      default:
        return <Badge variant="secondary">{ruolo}</Badge>
    }
  }

  const getStatusBadge = (abilitato: boolean, data_scadenza?: string) => {
    if (!abilitato) {
      return <Badge className="bg-red-100 text-red-800">Disabilitato</Badge>
    }
    
    if (data_scadenza) {
      const scadenza = new Date(data_scadenza)
      const oggi = new Date()
      
      if (scadenza < oggi) {
        return <Badge className="bg-red-100 text-red-800">Scaduto</Badge>
      } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {
        return <Badge className="bg-yellow-100 text-yellow-800">In Scadenza</Badge>
      }
    }
    
    return <Badge className="bg-green-100 text-green-800">Attivo</Badge>
  }

  const filteredUsers = users.filter(u =>
    u.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.ragione_sociale?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Verifica se l'utente ha permessi di amministrazione
  if (user?.ruolo !== 'owner') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-slate-900 mb-2">Accesso Negato</h2>
            <p className="text-slate-600">Non hai i permessi necessari per accedere a questa sezione.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">

        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <Settings className="h-8 w-8 text-blue-600" />
              Pannello Admin
            </h1>
            <p className="text-slate-600 mt-1">Questa sezione mostra la lista di tutti gli utenti del sistema.</p>
          </div>

          {activeTab === 'crea-utente' && (
            <PrimaryButton
              size="sm"
              onClick={() => setActiveTab('visualizza-utenti')}
              icon={<RefreshCw className="h-4 w-4" />}
              glow
            >
              Aggiorna
            </PrimaryButton>
          )}
        </div>

        {/* Tabs - Identiche al React */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={`grid w-full ${selectedUser ? 'grid-cols-7' : 'grid-cols-6'}`}>
            <TabsTrigger value="visualizza-utenti" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Visualizza Utenti
            </TabsTrigger>
            <TabsTrigger value="crea-utente" className="flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              Crea Nuovo Utente
            </TabsTrigger>
            {selectedUser && (
              <TabsTrigger value="modifica-utente" className="flex items-center gap-2">
                <Edit className="h-4 w-4" />
                Modifica Utente
              </TabsTrigger>
            )}
            <TabsTrigger value="accedi-come-utente" className="flex items-center gap-2">
              <LogIn className="h-4 w-4" />
              Accedi come Utente
            </TabsTrigger>
            <TabsTrigger value="database-tipologie-cavi" className="flex items-center gap-2">
              <Cable className="h-4 w-4" />
              Database Tipologie Cavi
            </TabsTrigger>
            <TabsTrigger value="visualizza-database-raw" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              Visualizza Database Raw
            </TabsTrigger>
            <TabsTrigger value="reset-database" className="flex items-center gap-2">
              <RotateCcw className="h-4 w-4" />
              Reset Database
            </TabsTrigger>
          </TabsList>

          {/* Tab Visualizza Utenti */}
          <TabsContent value="visualizza-utenti" className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-slate-900">Visualizza Utenti</h2>
                <p className="text-slate-600">Questa sezione mostra la lista di tutti gli utenti del sistema.</p>
              </div>
              <PrimaryButton
                onClick={loadData}
                loading={isLoading}
                icon={<RefreshCw className="h-4 w-4" />}
                size="sm"
              >
                Aggiorna
              </PrimaryButton>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-600">{error}</p>
              </div>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Lista Utenti</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Username</TableHead>
                        <TableHead>Password</TableHead>
                        <TableHead>Ruolo</TableHead>
                        <TableHead>Ragione Sociale</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>VAT</TableHead>
                        <TableHead>Nazione</TableHead>
                        <TableHead>Referente</TableHead>
                        <TableHead>Scadenza</TableHead>
                        <TableHead>Stato</TableHead>
                        <TableHead>Azioni</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={12} className="text-center py-8">
                            <div className="flex items-center justify-center gap-2">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Caricamento...
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : users.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={12} className="text-center py-8 text-slate-500">
                            Nessun utente trovato
                          </TableCell>
                        </TableRow>
                      ) : (
                        users.map((utente) => (
                          <TableRow key={utente.id_utente}>
                            <TableCell>{utente.id_utente}</TableCell>
                            <TableCell className="font-medium">{utente.username}</TableCell>
                            <TableCell>{utente.password_plain || '***'}</TableCell>
                            <TableCell>{getRuoloBadge(utente.ruolo)}</TableCell>
                            <TableCell>{utente.ragione_sociale || '-'}</TableCell>
                            <TableCell>{utente.email || '-'}</TableCell>
                            <TableCell>{utente.vat || '-'}</TableCell>
                            <TableCell>{utente.nazione || '-'}</TableCell>
                            <TableCell>{utente.referente_aziendale || '-'}</TableCell>
                            <TableCell>
                              {utente.data_scadenza ?
                                new Date(utente.data_scadenza).toLocaleDateString('it-IT') :
                                'N/A'
                              }
                            </TableCell>
                            <TableCell>{getStatusBadge(utente.abilitato, utente.data_scadenza)}</TableCell>
                            <TableCell>
                              <div className="flex gap-2">
                                <OutlineButton
                                  size="sm"
                                  onClick={() => handleEditUser(utente)}
                                  title="Modifica"
                                  icon={<Edit className="h-4 w-4" />}
                                >
                                  Modifica
                                </OutlineButton>
                                <AnimatedButton
                                  variant={utente.abilitato ? 'danger' : 'success'}
                                  size="sm"
                                  onClick={() => handleToggleUserStatus(utente.id_utente)}
                                  title={utente.abilitato ? 'Disabilita' : 'Abilita'}
                                  disabled={utente.ruolo === 'owner'}
                                  icon={utente.abilitato ? <Clock className="h-4 w-4" /> : <CheckCircle className="h-4 w-4" />}
                                >
                                  {utente.abilitato ? 'Disabilita' : 'Abilita'}
                                </AnimatedButton>
                                <DangerButton
                                  size="sm"
                                  onClick={() => handleDeleteUser(utente.id_utente)}
                                  title="Elimina"
                                  disabled={utente.ruolo === 'owner'}
                                  icon={<Trash2 className="h-4 w-4" />}
                                >
                                  Elimina
                                </DangerButton>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Crea Nuovo Utente */}
          <TabsContent value="crea-utente" className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900">Crea Nuovo Utente Standard</h2>
              <p className="text-slate-600">Da qui puoi creare un nuovo utente standard nel sistema.</p>
            </div>
            <UserForm
              user={null}
              onSave={handleSaveUser}
              onCancel={handleCancelForm}
            />
          </TabsContent>

          {/* Tab Modifica Utente - Visibile solo quando un utente è selezionato */}
          {selectedUser && (
            <TabsContent value="modifica-utente" className="space-y-4">
              <div>
                <h2 className="text-2xl font-bold text-slate-900">Modifica Utente: {selectedUser.username}</h2>
                <p className="text-slate-600">Da qui puoi modificare i dati dell'utente selezionato.</p>
              </div>
              <UserForm
                user={selectedUser}
                onSave={handleSaveUser}
                onCancel={handleCancelForm}
              />
            </TabsContent>
          )}

          {/* Tab Accedi come Utente */}
          <TabsContent value="accedi-come-utente" className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900">Accedi come Utente</h2>
              <p className="text-slate-600">Da qui puoi accedere al sistema impersonando un altro utente.</p>
            </div>
            <ImpersonateUser />
          </TabsContent>

          {/* Tab Database Tipologie Cavi */}
          <TabsContent value="database-tipologie-cavi" className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900">Database Tipologie Cavi</h2>
              <p className="text-slate-600">Gestisci il database enciclopedico delle tipologie di cavi: categorie, produttori, standard e tipologie.</p>
            </div>
            <TipologieCaviManager />
          </TabsContent>

          {/* Tab Visualizza Database Raw */}
          <TabsContent value="visualizza-database-raw" className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900">Visualizzazione Database Raw</h2>
              <p className="text-slate-600">Questa sezione mostra una visualizzazione raw del database. Puoi vedere i dati delle tabelle principali.</p>
            </div>
            <DatabaseView />
          </TabsContent>

          {/* Tab Reset Database */}
          <TabsContent value="reset-database" className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900">Reset Database</h2>
              <p className="text-slate-600">Attenzione: questa operazione cancellerà tutti i dati del database.</p>
            </div>
            <ResetDatabase />
          </TabsContent>

        </Tabs>
      </div>
    </div>
  )
}
