'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { AnimatedButton, PrimaryButton, SecondaryButton, DangerButton, OutlineButton, QuickButton } from '@/components/ui/animated-button'
import { UserActionsDropdown } from '@/components/ui/actions-dropdown'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { api, cantieriApi, usersApi } from '@/lib/api'
import { User, Cantiere } from '@/types'
import UserForm from '@/components/admin/UserForm'
import DatabaseView from '@/components/admin/DatabaseView'
import ResetDatabase from '@/components/admin/ResetDatabase'
import TipologieCaviManager from '@/components/admin/TipologieCaviManager'
import {
  Settings,
  Users,
  Building2,
  Search,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  Shield,
  Key,
  Loader2,
  UserPlus,
  LogIn,
  Cable,
  Database,
  RotateCcw,
  RefreshCw
} from 'lucide-react'

export default function AdminPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('visualizza-utenti')
  const [searchTerm, setSearchTerm] = useState('')
  const [users, setUsers] = useState<User[]>([])
  const [cantieri, setCantieri] = useState<Cantiere[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' })

  const { user, impersonateUser } = useAuth()

  // Carica dati dal backend
  useEffect(() => {
    loadData()
  }, [activeTab])

  const loadData = async () => {
    try {
      setIsLoading(true)
      setError('')

      console.log('Caricamento dati per tab:', activeTab)
      console.log('Token presente:', typeof window !== 'undefined' ? localStorage.getItem('token') : 'N/A')
      console.log('Utente corrente:', user)

      if (activeTab === 'visualizza-utenti' || activeTab === 'crea-utente' || activeTab === 'accedi-come-utente') {
        console.log('Chiamata API per ottenere utenti...')
        const usersData = await usersApi.getUsers()
        console.log('Utenti ricevuti:', usersData)
        setUsers(usersData)
      } else if (activeTab === 'cantieri') {
        const cantieriData = await cantieriApi.getCantieri()
        setCantieri(cantieriData)
      }
    } catch (error: any) {
      console.error('Errore caricamento dati:', error)
      console.error('Dettagli errore:', error.response)
      setError(error.response?.data?.detail || error.message || 'Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditUser = (userToEdit: User) => {
    setSelectedUser(userToEdit)
    setActiveTab('modifica-utente')
  }

  const handleToggleUserStatus = async (userId: number) => {
    try {
      await usersApi.toggleUserStatus(userId)
      loadData() // Ricarica i dati
    } catch (error: any) {
      console.error('Errore toggle status:', error)
      setError(error.response?.data?.detail || 'Errore durante la modifica dello stato utente')
    }
  }

  const handleDeleteUser = async (userId: number) => {
    if (confirm('Sei sicuro di voler eliminare questo utente?')) {
      try {
        await usersApi.deleteUser(userId)
        loadData() // Ricarica i dati
      } catch (error: any) {
        console.error('Errore eliminazione utente:', error)
        setError(error.response?.data?.detail || 'Errore durante l\'eliminazione dell\'utente')
      }
    }
  }

  const handleSaveUser = (savedUser: User) => {
    console.log('Utente salvato:', savedUser)
    setSelectedUser(null)
    setActiveTab('visualizza-utenti')
    loadData() // Ricarica i dati
  }

  const handleCancelForm = () => {
    setSelectedUser(null)
    setActiveTab('visualizza-utenti')
  }

  const handleQuickImpersonate = async (targetUser: User) => {
    try {
      console.log('Impersonificazione rapida utente:', targetUser.username, 'Ruolo:', targetUser.ruolo)

      await impersonateUser(targetUser.id_utente)

      // Reindirizza in base al ruolo dell'utente impersonato
      if (targetUser.ruolo === 'user') {
        router.push('/cantieri')
      } else if (targetUser.ruolo === 'cantieri_user') {
        router.push('/cavi')
      } else {
        router.push('/')
      }
    } catch (error: any) {
      console.error('Errore durante l\'impersonificazione rapida:', error)
      setError(error.response?.data?.detail || error.message || 'Errore durante l\'impersonificazione')
    }
  }

  // Helper functions per i badge

  const getRuoloBadge = (ruolo: string) => {
    switch (ruolo) {
      case 'owner':
        return <Badge className="bg-purple-100 text-purple-800">Owner</Badge>
      case 'user':
        return <Badge className="bg-blue-100 text-blue-800">User</Badge>
      case 'cantieri_user':
        return <Badge className="bg-green-100 text-green-800">Cantieri User</Badge>
      default:
        return <Badge variant="secondary">{ruolo}</Badge>
    }
  }

  const getStatusBadge = (abilitato: boolean, data_scadenza?: string) => {
    if (!abilitato) {
      return <Badge className="bg-red-100 text-red-800">Disabilitato</Badge>
    }
    
    if (data_scadenza) {
      const scadenza = new Date(data_scadenza)
      const oggi = new Date()
      
      if (scadenza < oggi) {
        return <Badge className="bg-red-100 text-red-800">Scaduto</Badge>
      } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {
        return <Badge className="bg-yellow-100 text-yellow-800">In Scadenza</Badge>
      }
    }
    
    return <Badge className="bg-green-100 text-green-800">Attivo</Badge>
  }

  const filteredUsers = users.filter(u =>
    u.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.ragione_sociale?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Verifica se l'utente ha permessi di amministrazione
  if (user?.ruolo !== 'owner') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-slate-900 mb-2">Accesso Negato</h2>
            <p className="text-slate-600">Non hai i permessi necessari per accedere a questa sezione.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[90%] mx-auto space-y-6">



        {/* Tabs - Stile sottile senza hover invasivi */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={`grid w-full ${selectedUser ? 'grid-cols-6' : 'grid-cols-5'}`}>
            <TabsTrigger value="visualizza-utenti" className="tab-trigger flex items-center gap-2">
              <Users className="h-4 w-4" />
              Visualizza Utenti
            </TabsTrigger>
            <TabsTrigger value="crea-utente" className="tab-trigger flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              Crea Nuovo Utente
            </TabsTrigger>
            {selectedUser && (
              <TabsTrigger value="modifica-utente" className="tab-trigger flex items-center gap-2">
                <Edit className="h-4 w-4" />
                Modifica Utente
              </TabsTrigger>
            )}
            <TabsTrigger value="database-tipologie-cavi" className="tab-trigger flex items-center gap-2">
              <Cable className="h-4 w-4" />
              Database Tipologie Cavi
            </TabsTrigger>
            <TabsTrigger value="visualizza-database-raw" className="tab-trigger flex items-center gap-2">
              <Database className="h-4 w-4" />
              Visualizza Database Raw
            </TabsTrigger>
            <TabsTrigger value="reset-database" className="tab-trigger flex items-center gap-2">
              <RotateCcw className="h-4 w-4" />
              Reset Database
            </TabsTrigger>
          </TabsList>

          {/* Tab Visualizza Utenti */}
          <TabsContent value="visualizza-utenti" className="space-y-4">

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-600">{error}</p>
              </div>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Lista Utenti</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Username</TableHead>
                        <TableHead>Password</TableHead>
                        <TableHead>Ruolo</TableHead>
                        <TableHead>Ragione Sociale</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>VAT</TableHead>
                        <TableHead>Nazione</TableHead>
                        <TableHead>Referente</TableHead>
                        <TableHead>Scadenza</TableHead>
                        <TableHead>Stato</TableHead>
                        <TableHead>Azioni</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={12} className="text-center py-8">
                            <div className="flex items-center justify-center gap-2">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Caricamento...
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : users.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={12} className="text-center py-8 text-slate-500">
                            Nessun utente trovato
                          </TableCell>
                        </TableRow>
                      ) : (
                        users.map((utente) => (
                          <TableRow key={utente.id_utente}>
                            <TableCell>{utente.id_utente}</TableCell>
                            <TableCell className="font-medium">{utente.username}</TableCell>
                            <TableCell>{utente.password_plain || '***'}</TableCell>
                            <TableCell>{getRuoloBadge(utente.ruolo)}</TableCell>
                            <TableCell>{utente.ragione_sociale || '-'}</TableCell>
                            <TableCell>{utente.email || '-'}</TableCell>
                            <TableCell>{utente.vat || '-'}</TableCell>
                            <TableCell>{utente.nazione || '-'}</TableCell>
                            <TableCell>{utente.referente_aziendale || '-'}</TableCell>
                            <TableCell>
                              {utente.data_scadenza ?
                                new Date(utente.data_scadenza).toLocaleDateString('it-IT') :
                                'N/A'
                              }
                            </TableCell>
                            <TableCell>{getStatusBadge(utente.abilitato, utente.data_scadenza)}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2 justify-end">
                                {/* Dropdown azioni */}
                                <UserActionsDropdown
                                  user={utente}
                                  onEdit={() => handleEditUser(utente)}
                                  onToggleStatus={() => handleToggleUserStatus(utente.id_utente)}
                                  onDelete={() => handleDeleteUser(utente.id_utente)}
                                />

                                {/* Pulsante Entra */}
                                <PrimaryButton
                                  size="sm"
                                  onClick={() => handleQuickImpersonate(utente)}
                                  disabled={utente.ruolo === 'owner' || !utente.abilitato}
                                  className="px-3 py-1.5 text-xs"
                                  icon={<LogIn className="h-3.5 w-3.5" />}
                                >
                                  Entra
                                </PrimaryButton>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Crea Nuovo Utente */}
          <TabsContent value="crea-utente" className="space-y-4">
            <UserForm
              user={null}
              onSave={handleSaveUser}
              onCancel={handleCancelForm}
            />
          </TabsContent>

          {/* Tab Modifica Utente - Visibile solo quando un utente è selezionato */}
          {selectedUser && (
            <TabsContent value="modifica-utente" className="space-y-4">
              <UserForm
                user={selectedUser}
                onSave={handleSaveUser}
                onCancel={handleCancelForm}
              />
            </TabsContent>
          )}



          {/* Tab Database Tipologie Cavi */}
          <TabsContent value="database-tipologie-cavi" className="space-y-4">
            <TipologieCaviManager />
          </TabsContent>

          {/* Tab Visualizza Database Raw */}
          <TabsContent value="visualizza-database-raw" className="space-y-4">
            <DatabaseView />
          </TabsContent>

          {/* Tab Reset Database */}
          <TabsContent value="reset-database" className="space-y-4">
            <ResetDatabase />
          </TabsContent>

        </Tabs>
      </div>
    </div>
  )
}
