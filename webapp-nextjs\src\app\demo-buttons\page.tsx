'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  AnimatedButton, 
  PrimaryButton, 
  SecondaryButton, 
  SuccessButton, 
  DangerButton, 
  OutlineButton 
} from '@/components/ui/animated-button'
import { 
  Save, 
  Download, 
  Upload, 
  Trash2, 
  Edit, 
  Plus, 
  RefreshCw,
  Settings,
  User,
  Database
} from 'lucide-react'

export default function DemoButtonsPage() {
  const [loading, setLoading] = useState(false)

  const handleClick = (buttonName: string) => {
    console.log(`Clicked: ${buttonName}`)
    setLoading(true)
    setTimeout(() => setLoading(false), 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-6xl mx-auto space-y-8">
        
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-slate-900 mb-4">
            🎨 Demo Pulsanti Animati
          </h1>
          <p className="text-lg text-slate-600">
            Esempi di tutti i pulsanti animati con effetti hover futuristici
          </p>
        </div>

        {/* Pulsanti Primari */}
        <Card>
          <CardHeader>
            <CardTitle>Pulsanti Primari</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <PrimaryButton 
                onClick={() => handleClick('Primary')}
                icon={<Save className="h-4 w-4" />}
              >
                Salva
              </PrimaryButton>
              
              <PrimaryButton 
                onClick={() => handleClick('Primary Glow')}
                icon={<Download className="h-4 w-4" />}
                glow
              >
                Download con Glow
              </PrimaryButton>
              
              <PrimaryButton 
                onClick={() => handleClick('Primary Loading')}
                loading={loading}
                icon={<RefreshCw className="h-4 w-4" />}
              >
                Con Loading
              </PrimaryButton>
            </div>
          </CardContent>
        </Card>

        {/* Pulsanti Secondari */}
        <Card>
          <CardHeader>
            <CardTitle>Pulsanti Secondari</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <SecondaryButton 
                onClick={() => handleClick('Secondary')}
                icon={<Edit className="h-4 w-4" />}
              >
                Modifica
              </SecondaryButton>
              
              <SecondaryButton 
                onClick={() => handleClick('Secondary Small')}
                icon={<Settings className="h-4 w-4" />}
                size="sm"
              >
                Piccolo
              </SecondaryButton>
              
              <SecondaryButton 
                onClick={() => handleClick('Secondary Large')}
                icon={<User className="h-4 w-4" />}
                size="lg"
              >
                Grande
              </SecondaryButton>
            </div>
          </CardContent>
        </Card>

        {/* Pulsanti di Successo */}
        <Card>
          <CardHeader>
            <CardTitle>Pulsanti di Successo</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <SuccessButton 
                onClick={() => handleClick('Success')}
                icon={<Plus className="h-4 w-4" />}
              >
                Crea Nuovo
              </SuccessButton>
              
              <SuccessButton 
                onClick={() => handleClick('Success Glow')}
                icon={<Upload className="h-4 w-4" />}
                glow
              >
                Upload con Glow
              </SuccessButton>
            </div>
          </CardContent>
        </Card>

        {/* Pulsanti di Pericolo */}
        <Card>
          <CardHeader>
            <CardTitle>Pulsanti di Pericolo</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <DangerButton 
                onClick={() => handleClick('Danger')}
                icon={<Trash2 className="h-4 w-4" />}
              >
                Elimina
              </DangerButton>
              
              <DangerButton 
                onClick={() => handleClick('Danger Glow')}
                icon={<Database className="h-4 w-4" />}
                glow
              >
                Reset Database
              </DangerButton>
            </div>
          </CardContent>
        </Card>

        {/* Pulsanti Outline */}
        <Card>
          <CardHeader>
            <CardTitle>Pulsanti Outline</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <OutlineButton 
                onClick={() => handleClick('Outline')}
                icon={<Edit className="h-4 w-4" />}
              >
                Modifica
              </OutlineButton>
              
              <OutlineButton 
                onClick={() => handleClick('Outline Small')}
                icon={<Settings className="h-4 w-4" />}
                size="sm"
              >
                Impostazioni
              </OutlineButton>
            </div>
          </CardContent>
        </Card>

        {/* Esempi Combinati */}
        <Card>
          <CardHeader>
            <CardTitle>Esempi Combinati</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-4 justify-center">
              <PrimaryButton 
                onClick={() => handleClick('Combined 1')}
                icon={<Save className="h-4 w-4" />}
                glow
                size="lg"
              >
                Salva Principale
              </PrimaryButton>
              
              <SecondaryButton 
                onClick={() => handleClick('Combined 2')}
                icon={<Edit className="h-4 w-4" />}
              >
                Annulla
              </SecondaryButton>
              
              <DangerButton 
                onClick={() => handleClick('Combined 3')}
                icon={<Trash2 className="h-4 w-4" />}
                size="sm"
              >
                Elimina
              </DangerButton>
            </div>
          </CardContent>
        </Card>

        {/* Informazioni Tailwind */}
        <Card>
          <CardHeader>
            <CardTitle>🎯 Come Usare Tailwind CSS</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-slate-50 rounded-lg p-4 text-sm">
              <h4 className="font-medium mb-2">Classi Tailwind Principali:</h4>
              <ul className="space-y-1 text-slate-600">
                <li><code className="bg-slate-200 px-1 rounded">bg-blue-500</code> - Colore di sfondo</li>
                <li><code className="bg-slate-200 px-1 rounded">hover:bg-blue-600</code> - Colore hover</li>
                <li><code className="bg-slate-200 px-1 rounded">transition-all duration-300</code> - Animazioni</li>
                <li><code className="bg-slate-200 px-1 rounded">transform hover:scale-105</code> - Effetti di scala</li>
                <li><code className="bg-slate-200 px-1 rounded">shadow-lg hover:shadow-xl</code> - Ombre</li>
              </ul>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}
