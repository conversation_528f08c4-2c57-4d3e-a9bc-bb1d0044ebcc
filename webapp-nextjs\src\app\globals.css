@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-jetbrains-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Pulsanti Primari - Stile Elegante senza ingrandimento */
  .btn-primary {
    @apply relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg
           transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25
           focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .btn-primary::before {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent
           translate-x-[-100%] transition-transform duration-700 ease-in-out;
    content: '';
  }

  .btn-primary:hover::before {
    @apply translate-x-[100%];
  }

  /* Pulsanti Secondari */
  .btn-secondary {
    @apply relative overflow-hidden bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium px-6 py-3 rounded-lg
           transition-all duration-300 ease-in-out hover:shadow-md
           focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2;
  }

  .btn-secondary::before {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent
           translate-x-[-100%] transition-transform duration-700 ease-in-out;
    content: '';
  }

  .btn-secondary:hover::before {
    @apply translate-x-[100%];
  }

  /* Pulsanti di Successo */
  .btn-success {
    @apply relative overflow-hidden bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg
           transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-green-500/25
           focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
  }

  .btn-success::before {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent
           translate-x-[-100%] transition-transform duration-700 ease-in-out;
    content: '';
  }

  .btn-success:hover::before {
    @apply translate-x-[100%];
  }

  /* Pulsanti di Pericolo */
  .btn-danger {
    @apply relative overflow-hidden bg-red-600 hover:bg-red-700 text-white font-medium px-6 py-3 rounded-lg
           transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-red-500/25
           focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
  }

  .btn-danger::before {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent
           translate-x-[-100%] transition-transform duration-700 ease-in-out;
    content: '';
  }

  .btn-danger:hover::before {
    @apply translate-x-[100%];
  }

  /* Pulsanti Outline */
  .btn-outline {
    @apply relative overflow-hidden bg-transparent hover:bg-blue-50 text-blue-600 border-2 border-blue-600
           hover:border-blue-700 font-medium px-6 py-3 rounded-lg
           transition-all duration-300 ease-in-out hover:shadow-md
           focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .btn-outline::before {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/10 to-transparent
           translate-x-[-100%] transition-transform duration-700 ease-in-out;
    content: '';
  }

  .btn-outline:hover::before {
    @apply translate-x-[100%];
  }

  /* Pulsanti Piccoli - Per tasti rapidi non invasivi */
  .btn-sm {
    @apply px-4 py-2 text-sm;
  }

  /* Pulsanti Grandi */
  .btn-lg {
    @apply px-8 py-4 text-lg;
  }

  /* Pulsanti Rapidi - Stile sottile e non invasivo */
  .btn-quick {
    @apply relative bg-transparent hover:bg-slate-100 text-slate-600 hover:text-slate-900
           hover:font-semibold transition-all duration-200 ease-in-out
           focus:outline-none focus:ring-1 focus:ring-slate-300 focus:ring-offset-1;
  }

  /* Effetto Glow per pulsanti importanti */
  .btn-glow {
    @apply shadow-lg hover:shadow-xl;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .btn-glow:hover {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }

  /* Animazioni per icone nei pulsanti - Solo per pulsanti principali */
  .btn-icon {
    @apply transition-all duration-300 ease-in-out;
  }

  .btn-primary:hover .btn-icon,
  .btn-secondary:hover .btn-icon,
  .btn-success:hover .btn-icon,
  .btn-danger:hover .btn-icon,
  .btn-outline:hover .btn-icon {
    @apply brightness-110;
  }

  /* Tab styling - Hover marcato e contrasto migliorato */
  .tab-trigger {
    @apply transition-all duration-200 ease-in-out relative;
  }

  .tab-trigger:hover {
    @apply text-slate-800 bg-slate-50 font-medium;
  }

  .tab-trigger[data-state="active"] {
    @apply bg-white text-slate-900 shadow-md border-b-2 border-blue-500 font-semibold;
  }

  /* Effetto sottile per tab hover */
  .tab-trigger:hover:not([data-state="active"]) {
    @apply bg-gradient-to-b from-slate-50 to-slate-100;
  }
}
