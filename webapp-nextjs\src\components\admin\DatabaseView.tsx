'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { usersApi } from '@/lib/api'
import { Loader2, Database, RefreshCw, Eye } from 'lucide-react'

export default function DatabaseView() {
  const [activeTable, setActiveTable] = useState('users')
  const [data, setData] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Carica i dati della tabella selezionata
  const loadTableData = async (tableName: string) => {
    setLoading(true)
    setError('')
    
    try {
      // Per ora mostriamo solo la tabella utenti
      // In futuro si può estendere per altre tabelle
      if (tableName === 'users') {
        const users = await usersApi.getUsers()
        setData(users)
      } else {
        // Placeholder per altre tabelle
        setData([])
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Errore durante il caricamento dei dati')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadTableData(activeTable)
  }, [activeTable])

  const renderTableContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Caricamento dati...</span>
        </div>
      )
    }

    if (error) {
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )
    }

    if (data.length === 0) {
      return (
        <div className="text-center py-8 text-slate-500">
          Nessun dato trovato per la tabella {activeTable}
        </div>
      )
    }

    // Ottieni le chiavi del primo oggetto per le colonne
    const columns = Object.keys(data[0])

    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column} className="font-medium">
                  {column}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((row, index) => (
              <TableRow key={index}>
                {columns.map((column) => (
                  <TableCell key={column} className="font-mono text-sm">
                    {row[column] !== null && row[column] !== undefined 
                      ? String(row[column]) 
                      : <span className="text-slate-400">NULL</span>
                    }
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Visualizzazione Database Raw
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadTableData(activeTable)}
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Aggiorna
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Eye className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Visualizzazione Raw del Database</h4>
              <p className="text-sm text-blue-700 mt-1">
                Questa sezione mostra i dati grezzi delle tabelle del database. 
                Utile per debugging e analisi dei dati.
              </p>
            </div>
          </div>
        </div>

        <Tabs value={activeTable} onValueChange={setActiveTable} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="users">Utenti</TabsTrigger>
            <TabsTrigger value="cantieri">Cantieri</TabsTrigger>
            <TabsTrigger value="cavi">Cavi</TabsTrigger>
            <TabsTrigger value="bobine">Bobine</TabsTrigger>
          </TabsList>

          <TabsContent value="users" className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Tabella: users</h3>
              <p className="text-sm text-slate-600">
                Contiene tutti gli utenti del sistema con i loro dati di accesso e informazioni aziendali.
              </p>
            </div>
            {renderTableContent()}
          </TabsContent>

          <TabsContent value="cantieri" className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Tabella: cantieri</h3>
              <p className="text-sm text-slate-600">
                Contiene tutti i cantieri/progetti gestiti nel sistema.
              </p>
            </div>
            <div className="text-center py-8 text-slate-500">
              Visualizzazione cantieri - Da implementare
            </div>
          </TabsContent>

          <TabsContent value="cavi" className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Tabella: cavi</h3>
              <p className="text-sm text-slate-600">
                Contiene tutti i cavi installati nei vari cantieri.
              </p>
            </div>
            <div className="text-center py-8 text-slate-500">
              Visualizzazione cavi - Da implementare
            </div>
          </TabsContent>

          <TabsContent value="bobine" className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Tabella: bobine</h3>
              <p className="text-sm text-slate-600">
                Contiene tutte le bobine disponibili nel parco cavi.
              </p>
            </div>
            <div className="text-center py-8 text-slate-500">
              Visualizzazione bobine - Da implementare
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
