'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { PrimaryButton, SecondaryButton } from '@/components/ui/animated-button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { usersApi } from '@/lib/api'
import { User } from '@/types'
import { Loader2, Save, X } from 'lucide-react'

interface UserFormProps {
  user?: User | null
  onSave: (user: User) => void
  onCancel: () => void
}

export default function UserForm({ user, onSave, onCancel }: UserFormProps) {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    ruolo: 'user', // L'amministratore può creare solo utenti standard
    data_scadenza: '',
    abilitato: true,
    // Nuovi campi aziendali
    ragione_sociale: '',
    indirizzo: '',
    nazione: '',
    email: '',
    vat: '',
    referente_aziendale: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Inizializza il form con i dati dell'utente se presente
  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username || '',
        password: '', // Non mostrare la password esistente
        ruolo: user.ruolo || 'user',
        data_scadenza: user.data_scadenza ? user.data_scadenza.split('T')[0] : '',
        abilitato: user.abilitato !== undefined ? user.abilitato : true,
        // Nuovi campi aziendali
        ragione_sociale: user.ragione_sociale || '',
        indirizzo: user.indirizzo || '',
        nazione: user.nazione || '',
        email: user.email || '',
        vat: user.vat || '',
        referente_aziendale: user.referente_aziendale || ''
      })
    }
  }, [user])

  // Gestisce il cambio dei valori del form
  const handleChange = (name: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Rimuovi l'errore per questo campo se presente
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  // Validazione del form
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.username.trim()) {
      newErrors.username = 'Username è obbligatorio'
    }

    if (!user && !formData.password.trim()) {
      newErrors.password = 'Password è obbligatoria per nuovi utenti'
    }

    if (!formData.ragione_sociale.trim()) {
      newErrors.ragione_sociale = 'Ragione sociale è obbligatoria'
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email non valida'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Gestisce il submit del form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError('')

    try {
      // Prepara i dati da inviare
      const userData = {
        ...formData
      }

      // Per nuovi utenti, forza sempre il ruolo "user"
      if (!user) {
        userData.ruolo = 'user'
      }

      // Rimuovi la password se è vuota (modifica utente)
      if (user && !userData.password.trim()) {
        delete (userData as any).password
      }

      // Converti la data in formato ISO se presente
      if (userData.data_scadenza) {
        userData.data_scadenza = userData.data_scadenza
      }

      let result
      if (user) {
        // Aggiorna l'utente esistente
        result = await usersApi.updateUser(user.id_utente, userData)
      } else {
        // Crea un nuovo utente
        result = await usersApi.createUser(userData)
      }

      onSave(result)
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Errore durante il salvataggio dell\'utente')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {user ? `Modifica Utente: ${user.username}` : 'Crea Nuovo Utente Standard'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Username */}
            <div className="space-y-2">
              <Label htmlFor="username">Username *</Label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => handleChange('username', e.target.value)}
                disabled={loading}
                className={errors.username ? 'border-red-500' : ''}
              />
              {errors.username && <p className="text-sm text-red-600">{errors.username}</p>}
            </div>

            {/* Password */}
            <div className="space-y-2">
              <Label htmlFor="password">
                {user ? 'Nuova Password (lascia vuoto per non modificare)' : 'Password *'}
              </Label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleChange('password', e.target.value)}
                disabled={loading}
                className={errors.password ? 'border-red-500' : ''}
              />
              {errors.password && <p className="text-sm text-red-600">{errors.password}</p>}
            </div>

            {/* Ragione Sociale */}
            <div className="space-y-2">
              <Label htmlFor="ragione_sociale">Ragione Sociale *</Label>
              <Input
                id="ragione_sociale"
                value={formData.ragione_sociale}
                onChange={(e) => handleChange('ragione_sociale', e.target.value)}
                disabled={loading}
                className={errors.ragione_sociale ? 'border-red-500' : ''}
              />
              {errors.ragione_sociale && <p className="text-sm text-red-600">{errors.ragione_sociale}</p>}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleChange('email', e.target.value)}
                disabled={loading}
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && <p className="text-sm text-red-600">{errors.email}</p>}
            </div>

            {/* Indirizzo */}
            <div className="space-y-2">
              <Label htmlFor="indirizzo">Indirizzo</Label>
              <Input
                id="indirizzo"
                value={formData.indirizzo}
                onChange={(e) => handleChange('indirizzo', e.target.value)}
                disabled={loading}
              />
            </div>

            {/* Nazione */}
            <div className="space-y-2">
              <Label htmlFor="nazione">Nazione</Label>
              <Input
                id="nazione"
                value={formData.nazione}
                onChange={(e) => handleChange('nazione', e.target.value)}
                disabled={loading}
              />
            </div>

            {/* VAT */}
            <div className="space-y-2">
              <Label htmlFor="vat">VAT</Label>
              <Input
                id="vat"
                value={formData.vat}
                onChange={(e) => handleChange('vat', e.target.value)}
                disabled={loading}
              />
            </div>

            {/* Referente Aziendale */}
            <div className="space-y-2">
              <Label htmlFor="referente_aziendale">Referente Aziendale</Label>
              <Input
                id="referente_aziendale"
                value={formData.referente_aziendale}
                onChange={(e) => handleChange('referente_aziendale', e.target.value)}
                disabled={loading}
              />
            </div>

            {/* Data Scadenza */}
            <div className="space-y-2">
              <Label htmlFor="data_scadenza">Data Scadenza</Label>
              <Input
                id="data_scadenza"
                type="date"
                value={formData.data_scadenza}
                onChange={(e) => handleChange('data_scadenza', e.target.value)}
                disabled={loading}
              />
            </div>

            {/* Ruolo - Solo per modifica, per creazione è sempre "user" */}
            {user ? (
              <div className="space-y-2">
                <Label htmlFor="ruolo">Ruolo</Label>
                <Select
                  value={formData.ruolo}
                  onValueChange={(value) => handleChange('ruolo', value)}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="cantieri_user">Cantieri User</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="ruolo">Ruolo</Label>
                <div className="px-3 py-2 bg-slate-50 border border-slate-200 rounded-md text-sm text-slate-600">
                  User (Standard)
                </div>
              </div>
            )}
          </div>

          {/* Utente Abilitato */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="abilitato"
              checked={formData.abilitato}
              onCheckedChange={(checked) => handleChange('abilitato', checked)}
              disabled={loading || (user && user.ruolo === 'owner')}
            />
            <Label htmlFor="abilitato">Utente abilitato</Label>
          </div>

          {/* Pulsanti */}
          <div className="flex justify-end space-x-4 pt-6">
            <SecondaryButton
              type="button"
              onClick={onCancel}
              disabled={loading}
              icon={<X className="h-4 w-4" />}
            >
              Annulla
            </SecondaryButton>
            <PrimaryButton
              type="submit"
              loading={loading}
              icon={<Save className="h-4 w-4" />}
              glow
            >
              {loading ? 'Salvataggio...' : 'Salva'}
            </PrimaryButton>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
