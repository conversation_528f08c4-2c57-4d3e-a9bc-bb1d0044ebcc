'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { PrimaryButton, QuickButton } from '@/components/ui/animated-button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import {
  Cable,
  Home,
  Activity,
  BarChart3,
  Settings,
  Users,
  Menu,
  X,
  Building2,
  ClipboardList,
  FileText,
  LogOut,
  Package
} from 'lucide-react'

const getNavigation = (userRole: string | undefined, isImpersonating: boolean, impersonatedUser: any) => {
  // Home button - testo personalizzato come nella webapp originale
  const homeButton = {
    name: isImpersonating ? "Menu Admin" :
          userRole === 'owner' ? "Menu Admin" :
          userRole === 'user' ? "Lista Cantieri" :
          userRole === 'cantieri_user' ? "Gestione Cavi" : "Home",
    href: userRole === 'owner' ? '/admin' :
          userRole === 'user' ? '/cantieri' :
          userRole === 'cantieri_user' ? '/cavi' : '/',
    icon: Home
  }

  if (userRole === 'owner' && !isImpersonating) {
    // Solo amministratore - solo il pulsante Home che va al pannello admin
    return [homeButton]
  }

  if (userRole === 'user' || (isImpersonating && impersonatedUser?.role === 'user')) {
    // Utente standard - Home + eventualmente cantieri se impersonificato
    const nav = [homeButton]
    if (isImpersonating) {
      nav.push({ name: 'Cantieri', href: '/cantieri', icon: Building2 })
    }
    return nav
  }

  if (userRole === 'cantieri_user' || (isImpersonating && impersonatedUser?.role === 'cantieri_user')) {
    // Utente cantiere - menu completo come nella webapp originale
    const nav = [homeButton]

    // Se non è cantieri_user diretto, aggiungi Visualizza Cavi
    if (userRole !== 'cantieri_user') {
      nav.push({ name: 'Visualizza Cavi', href: '/cavi', icon: Cable })
    }

    nav.push(
      { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },
      { name: 'Gestione Excel', href: '/excel', icon: FileText },
      { name: 'Report', href: '/reports', icon: BarChart3 },
      { name: 'Comande', href: '/comande', icon: ClipboardList },
      { name: 'Produttività', href: '/productivity', icon: Activity },
    )
    return nav
  }

  // Default
  return [homeButton]
}

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()
  const { user, cantiere, isAuthenticated, isImpersonating, impersonatedUser, logout } = useAuth()
  const navigation = getNavigation(user?.ruolo, isImpersonating, impersonatedUser)

  // Non mostrare navbar nella pagina di login
  if (pathname === '/login') {
    return null
  }

  // Se non autenticato, non mostrare navbar
  if (!isAuthenticated) {
    return null
  }

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm">
      <div className="max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">

          {/* Tutto a sinistra: Logo + Navigation */}
          <div className="flex items-center space-x-4">
            {/* Logo e Brand */}
            <div className="flex items-center space-x-3 cursor-default">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
                <Cable className="w-5 h-5 text-white" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-slate-900">CABLYS</h1>
                <p className="text-xs text-slate-500 -mt-1">Cable Installation System</p>
              </div>
            </div>

            {/* Navigation Desktop */}
            <div className="hidden md:flex items-center space-x-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href || 
                             (item.href !== '/' && pathname.startsWith(item.href))
              const Icon = item.icon
              
              return (
                <Link key={item.name} href={item.href}>
                  {isActive ? (
                    <PrimaryButton
                      size="sm"
                      className="flex items-center space-x-2"
                      icon={<Icon className="w-4 h-4" />}
                    >
                      <span className="hidden lg:inline">{item.name}</span>
                    </PrimaryButton>
                  ) : (
                    <QuickButton
                      size="sm"
                      className="flex items-center space-x-2 px-3 py-2 text-slate-600 hover:text-slate-900"
                    >
                      <Icon className="w-4 h-4" />
                      <span className="hidden lg:inline">{item.name}</span>
                    </QuickButton>
                  )}
                </Link>
              )
            })}
            </div>
          </div>

          {/* User Info a destra con più margine */}
          <div className="flex items-center space-x-4 ml-8">
            {/* Indicatore impersonificazione come nella webapp originale */}
            {isImpersonating && impersonatedUser && (
              <p className="text-sm text-slate-600">
                Accesso come: <span className="font-semibold">{impersonatedUser.username}</span>
              </p>
            )}

            <div className="hidden sm:flex items-center space-x-3">
              <div className="text-right">
                <p className="text-sm font-medium text-slate-900">
                  {user ? user.username : cantiere?.commessa}
                </p>
                <p className="text-xs text-slate-500">
                  {user ? user.ruolo : 'Cantiere'}
                </p>
              </div>
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                {user ? <Users className="w-4 h-4 text-white" /> : <Building2 className="w-4 h-4 text-white" />}
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                Online
              </Badge>
              <Button variant="ghost" size="sm" onClick={logout} title={isImpersonating ? 'Torna al menu admin' : 'Logout'}>
                <LogOut className="w-4 h-4" />
              </Button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(!isOpen)}
                className="text-slate-600"
              >
                {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden border-t border-slate-200 bg-white">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href || 
                             (item.href !== '/' && pathname.startsWith(item.href))
              const Icon = item.icon
              
              return (
                <Link key={item.name} href={item.href}>
                  {isActive ? (
                    <PrimaryButton
                      size="sm"
                      className="w-full justify-start space-x-3"
                      onClick={() => setIsOpen(false)}
                      icon={<Icon className="w-4 h-4" />}
                    >
                      {item.name}
                    </PrimaryButton>
                  ) : (
                    <QuickButton
                      size="sm"
                      className="w-full justify-start space-x-3 text-slate-600 hover:text-slate-900"
                      onClick={() => setIsOpen(false)}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{item.name}</span>
                    </QuickButton>
                  )}
                </Link>
              )
            })}
          </div>
          
          {/* Mobile User Info */}
          <div className="border-t border-slate-200 px-4 py-3">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                {user ? <Users className="w-4 h-4 text-white" /> : <Building2 className="w-4 h-4 text-white" />}
              </div>
              <div>
                <p className="text-sm font-medium text-slate-900">
                  {user ? user.username : cantiere?.commessa}
                </p>
                <p className="text-xs text-slate-500">
                  {user ? user.ruolo : 'Cantiere'}
                </p>
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-800 ml-auto">
                Online
              </Badge>
            </div>
          </div>
        </div>
      )}
    </nav>
  )
}
