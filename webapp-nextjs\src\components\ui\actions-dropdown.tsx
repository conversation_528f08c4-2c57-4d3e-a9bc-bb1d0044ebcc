'use client'

import { useState, useRef, useEffect } from 'react'
import { ChevronDown, Edit, Clock, CheckCircle, Trash2 } from 'lucide-react'
import { QuickButton } from './animated-button'

interface Action {
  label: string
  icon: React.ReactNode
  onClick: () => void
  disabled?: boolean
  className?: string
}

interface ActionsDropdownProps {
  actions: Action[]
  disabled?: boolean
}

export default function ActionsDropdown({ actions, disabled = false }: ActionsDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Chiudi dropdown quando si clicca fuori
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleActionClick = (action: Action) => {
    action.onClick()
    setIsOpen(false)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Trigger Button */}
      <QuickButton
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className="p-1.5 bg-slate-50 hover:bg-slate-100 border border-slate-200 rounded-md"
        title="Azioni"
      >
        <ChevronDown className={`h-3.5 w-3.5 text-slate-600 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </QuickButton>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-1 w-36 bg-white border border-slate-200 rounded-md shadow-lg z-50">
          <div className="py-1">
            {actions.map((action, index) => (
              <button
                key={index}
                onClick={() => handleActionClick(action)}
                disabled={action.disabled}
                className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${
                  action.className || ''
                }`}
              >
                {action.icon}
                <span>{action.label}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Componente helper per azioni utente standard
interface UserActionsDropdownProps {
  user: {
    id_utente: number
    ruolo: string
    abilitato: boolean
  }
  onEdit: () => void
  onToggleStatus: () => void
  onDelete: () => void
}

export function UserActionsDropdown({ user, onEdit, onToggleStatus, onDelete }: UserActionsDropdownProps) {
  const actions: Action[] = [
    {
      label: 'Modifica',
      icon: <Edit className="h-3.5 w-3.5 text-slate-600" />,
      onClick: onEdit
    },
    {
      label: user.abilitato ? 'Disabilita' : 'Abilita',
      icon: user.abilitato 
        ? <Clock className="h-3.5 w-3.5 text-red-600" />
        : <CheckCircle className="h-3.5 w-3.5 text-green-600" />,
      onClick: onToggleStatus,
      disabled: user.ruolo === 'owner'
    },
    {
      label: 'Elimina',
      icon: <Trash2 className="h-3.5 w-3.5 text-red-600" />,
      onClick: onDelete,
      disabled: user.ruolo === 'owner',
      className: 'text-red-600 hover:bg-red-50'
    }
  ]

  return (
    <ActionsDropdown 
      actions={actions} 
      disabled={user.ruolo === 'owner' && actions.every(a => a.disabled)}
    />
  )
}
