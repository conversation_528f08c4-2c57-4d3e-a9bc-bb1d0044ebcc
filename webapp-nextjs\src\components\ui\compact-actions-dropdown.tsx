'use client'

import { useState, useRef, useEffect } from 'react'
import { ChevronDown, Edit, Clock, CheckCircle, Trash2 } from 'lucide-react'

interface CompactActionsDropdownProps {
  user: {
    id_utente: number
    ruolo: string
    abilitato: boolean
  }
  onEdit: () => void
  onToggleStatus: () => void
  onDelete: () => void
}

export default function CompactActionsDropdown({ 
  user, 
  onEdit, 
  onToggleStatus, 
  onDelete 
}: CompactActionsDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Chiudi dropdown quando si clicca fuori
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleAction = (action: () => void) => {
    action()
    setIsOpen(false)
  }

  const isDisabled = user.ruolo === 'owner'

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Triangolino nero */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isDisabled}
        className={`p-1.5 rounded hover:bg-slate-100 transition-colors ${
          isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
        }`}
        title="Azioni"
      >
        <ChevronDown className={`h-4 w-4 text-slate-800 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-1 w-32 bg-white border border-slate-200 rounded-md shadow-lg z-50">
          <div className="py-1">
            {/* Modifica */}
            <button
              onClick={() => handleAction(onEdit)}
              className="w-full px-3 py-2 text-left text-sm flex items-center gap-2 hover:bg-slate-50 transition-colors"
            >
              <Edit className="h-3.5 w-3.5 text-slate-600" />
              <span>Modifica</span>
            </button>

            {/* Abilita/Disabilita */}
            <button
              onClick={() => handleAction(onToggleStatus)}
              disabled={user.ruolo === 'owner'}
              className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 hover:bg-slate-50 transition-colors ${
                user.ruolo === 'owner' ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {user.abilitato ? (
                <>
                  <Clock className="h-3.5 w-3.5 text-red-500" />
                  <span>Disabilita</span>
                </>
              ) : (
                <>
                  <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                  <span>Abilita</span>
                </>
              )}
            </button>

            {/* Elimina */}
            <button
              onClick={() => handleAction(onDelete)}
              disabled={user.ruolo === 'owner'}
              className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 hover:bg-red-50 text-red-600 transition-colors ${
                user.ruolo === 'owner' ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <Trash2 className="h-3.5 w-3.5 text-red-500" />
              <span>Elimina</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
