'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { User, Cantier<PERSON> } from '@/types'
import { authApi } from '@/lib/api'

interface AuthContextType {
  user: User | null
  cantiere: Cantiere | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (username: string, password: string) => Promise<void>
  loginCantiere: (codice_cantiere: string, password_cantiere: string) => Promise<void>
  logout: () => void
  checkAuth: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [cantiere, setCantiere] = useState<Cantiere | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user || !!cantiere

  // Verifica l'autenticazione al caricamento
  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      console.log('Verificando autenticazione all\'avvio...')
      // Verifica se siamo nel browser
      if (typeof window === 'undefined') {
        setIsLoading(false)
        return
      }

      // Prima di tutto, imposta loading a true
      setIsLoading(true)

      // Pulisci eventuali token non validi o scaduti
      const token = localStorage.getItem('token')
      console.log('Token trovato nel localStorage:', token ? 'Sì' : 'No')

      if (token) {
        try {
          // Verifica la validità del token
          console.log('Tentativo di verifica token...')
          const userData = await authApi.verifyToken()
          console.log('Token valido, dati utente:', userData)

          // Imposta i dati dell'utente come nel sistema React originale
          const userInfo = {
            id_utente: userData.user_id,
            username: userData.username,
            ruolo: userData.role
          }
          setUser(userInfo)

          // Se è un utente cantiere, gestisci i dati del cantiere
          if (userData.role === 'cantieri_user' && userData.cantiere_id) {
            const cantiereData = {
              id_cantiere: userData.cantiere_id,
              commessa: userData.cantiere_name || `Cantiere ${userData.cantiere_id}`,
              codice_univoco: '',
              id_utente: userData.user_id
            }
            setCantiere(cantiereData)
          }
        } catch (tokenError) {
          console.error('Errore durante la verifica del token:', tokenError)
          // Se il token non è valido, rimuovilo
          console.log('Rimozione token non valido dal localStorage')
          localStorage.removeItem('token')
          localStorage.removeItem('access_token')
          localStorage.removeItem('user_data')
          localStorage.removeItem('cantiere_data')
          setUser(null)
          setCantiere(null)
        }
      } else {
        console.log('Nessun token trovato, utente non autenticato')
        setUser(null)
        setCantiere(null)
      }
    } catch (error) {
      console.error('Errore generale durante la verifica dell\'autenticazione:', error)
      // In caso di errore generale, assicurati che l'utente non sia autenticato
      if (typeof window !== 'undefined') {
        localStorage.removeItem('token')
        localStorage.removeItem('access_token')
        localStorage.removeItem('user_data')
        localStorage.removeItem('cantiere_data')
      }
      setUser(null)
      setCantiere(null)
    } finally {
      // Assicurati che loading sia impostato a false alla fine
      console.log('Completata verifica autenticazione, loading:', false)
      setTimeout(() => {
        setIsLoading(false)
      }, 500) // Aggiungi un piccolo ritardo come nel sistema React originale
    }
  }

  const login = async (username: string, password: string) => {
    try {
      console.log('Tentativo di login utente:', username)
      setIsLoading(true)
      const response = await authApi.login({ username, password })
      console.log('Risposta login ricevuta:', response)

      if (typeof window !== 'undefined') {
        // Salva il token come nel sistema React originale
        localStorage.setItem('token', response.access_token)

        // Il backend restituisce i dati dell'utente direttamente nella risposta
        const userData = {
          id_utente: response.user_id,
          username: response.username,
          ruolo: response.role
        }

        console.log('Impostazione dati utente:', userData)
        setUser(userData)
        setCantiere(null)

        return userData
      }
    } catch (error) {
      console.error('Errore login:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const loginCantiere = async (codice_cantiere: string, password_cantiere: string) => {
    try {
      console.log('Tentativo di login cantiere:', codice_cantiere)
      setIsLoading(true)
      const response = await authApi.loginCantiere({ codice_cantiere, password_cantiere })
      console.log('Risposta login cantiere ricevuta:', response)

      if (typeof window !== 'undefined') {
        // Salva il token come nel sistema React originale
        localStorage.setItem('token', response.access_token)

        // Il backend restituisce i dati del cantiere direttamente nella risposta
        const cantiereData = {
          id_cantiere: response.cantiere_id,
          commessa: response.cantiere_name,
          codice_univoco: codice_cantiere,
          id_utente: response.user_id
        }

        console.log('Impostazione dati cantiere:', cantiereData)
        setCantiere(cantiereData)
        setUser(null)

        return cantiereData
      }
    } catch (error) {
      console.error('Errore login cantiere:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    console.log('Logout normale, ritorno alla pagina di login')
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token')
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_data')
      localStorage.removeItem('cantiere_data')

      setUser(null)
      setCantiere(null)
      window.location.href = '/login'
    }
  }

  const value: AuthContextType = {
    user,
    cantiere,
    isAuthenticated,
    isLoading,
    login,
    loginCantiere,
    logout,
    checkAuth,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
